#!/usr/bin/env python3
"""
简单的真实数据测试脚本

快速验证项目是否获取真实金融数据
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_akshare_directly():
    """直接测试akshare"""
    print("=" * 50)
    print("直接测试akshare数据源")
    print("=" * 50)
    
    try:
        import akshare as ak
        print("✓ akshare已安装")
        
        # 测试获取股票信息
        print("\n测试获取平安银行(000001)信息...")
        stock_info = ak.stock_individual_info_em(symbol="000001")
        if not stock_info.empty:
            print("✓ 股票信息获取成功")
            print(f"  数据条数: {len(stock_info)}")
        else:
            print("✗ 股票信息为空")
        
        # 测试获取价格数据
        print("\n测试获取价格数据...")
        price_data = ak.stock_zh_a_hist(symbol="000001", period="daily", start_date="20250101", adjust="")
        if not price_data.empty:
            latest_price = price_data.iloc[-1]['收盘']
            print(f"✓ 价格数据获取成功")
            print(f"  最新收盘价: {latest_price:.2f}")
            print(f"  数据日期: {price_data.iloc[-1]['日期']}")
            return True
        else:
            print("✗ 价格数据为空")
            return False
            
    except ImportError:
        print("✗ akshare未安装")
        return False
    except Exception as e:
        print(f"✗ akshare测试失败: {str(e)}")
        return False

def test_yfinance_directly():
    """直接测试yfinance"""
    print("\n" + "=" * 50)
    print("直接测试yfinance数据源")
    print("=" * 50)
    
    try:
        import yfinance as yf
        print("✓ yfinance已安装")
        
        # 测试获取苹果股票
        print("\n测试获取苹果(AAPL)数据...")
        ticker = yf.Ticker("AAPL")
        
        # 获取基本信息
        info = ticker.info
        if info and 'symbol' in info:
            print("✓ 股票信息获取成功")
            print(f"  公司名称: {info.get('longName', 'N/A')}")
            print(f"  当前价格: ${info.get('currentPrice', 'N/A')}")
        
        # 获取历史数据
        hist = ticker.history(period="5d")
        if not hist.empty:
            latest_price = hist['Close'].iloc[-1]
            print(f"✓ 历史数据获取成功")
            print(f"  最新收盘价: ${latest_price:.2f}")
            return True
        else:
            print("✗ 历史数据为空")
            return False
            
    except ImportError:
        print("✗ yfinance未安装")
        return False
    except Exception as e:
        print(f"✗ yfinance测试失败: {str(e)}")
        return False

def test_project_integration():
    """测试项目集成"""
    print("\n" + "=" * 50)
    print("测试项目集成")
    print("=" * 50)
    
    try:
        from financial_analysis import StockDataProvider
        
        provider = StockDataProvider()
        print("✓ 股票数据提供者初始化成功")
        
        # 测试中国股票
        print("\n测试中国股票数据...")
        stock_info = provider.get_stock_info("000001", "SZSE")
        if stock_info:
            print(f"✓ 股票信息: {stock_info.name}")
            print(f"  交易所: {stock_info.exchange}")
            print(f"  市值: {stock_info.market_cap:,.0f}")
            
            # 简单测试价格获取（避免日期类型问题）
            try:
                prices = provider.get_stock_prices("000001", days=3, exchange="SZSE")
                if prices:
                    print(f"✓ 获取到 {len(prices)} 天的价格数据")
                    print(f"  最新价格: {prices[-1].close_price:.2f}")
                    return True
                else:
                    print("✗ 价格数据获取失败")
                    return False
            except Exception as e:
                print(f"⚠️ 价格数据获取异常: {str(e)}")
                return False
        else:
            print("✗ 股票信息获取失败")
            return False
            
    except Exception as e:
        print(f"✗ 项目集成测试失败: {str(e)}")
        return False

def test_news_integration():
    """测试新闻集成"""
    print("\n" + "=" * 50)
    print("测试新闻数据集成")
    print("=" * 50)
    
    try:
        from financial_analysis import NewsSearcher
        from financial_analysis.models import StockInfo
        
        searcher = NewsSearcher()
        print("✓ 新闻搜索器初始化成功")
        
        # 创建测试股票
        test_stock = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE",
            currency="CNY",
            sector="银行",
            market_cap=150000000000
        )
        
        print(f"\n搜索 {test_stock.name} 相关新闻...")
        news_items = searcher.search_stock_news(test_stock, days=7)
        
        if news_items:
            print(f"✓ 获取到 {len(news_items)} 条新闻")
            
            # 检查是否为模拟数据
            mock_indicators = ["发布最新财报，业绩超预期", "市场分析师看好"]
            is_mock = any(any(indicator in news.title for indicator in mock_indicators) for news in news_items)
            
            if is_mock:
                print("⚠️ 检测到模拟新闻数据")
                print("  原因: Windmill配置问题")
                return False
            else:
                print("✓ 疑似真实新闻数据")
                return True
        else:
            print("✗ 未获取到新闻数据")
            return False
            
    except Exception as e:
        print(f"✗ 新闻集成测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔍 简单数据源测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 测试各个组件
    results.append(("akshare直接测试", test_akshare_directly()))
    results.append(("yfinance直接测试", test_yfinance_directly()))
    results.append(("项目股票数据集成", test_project_integration()))
    results.append(("项目新闻数据集成", test_news_integration()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    # 给出建议
    if passed == total:
        print("\n🎉 恭喜！所有测试通过，您的项目已配置为使用真实数据")
    elif passed >= total * 0.5:
        print("\n⚠️ 部分测试通过，建议检查失败的组件")
    else:
        print("\n❌ 大部分测试失败，建议检查配置和依赖")
    
    print("\n💡 如需详细配置指导，请参考:")
    print("  - docs/troubleshooting.md")
    print("  - 运行 python setup_real_data.py")

if __name__ == "__main__":
    main()
