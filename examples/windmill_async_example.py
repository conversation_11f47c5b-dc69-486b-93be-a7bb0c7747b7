"""
Windmill 异步客户端使用示例

演示如何使用 Windmill 异步客户端进行各种操作，包括：
1. 基本的作业触发和等待
2. 文本分析生成
3. 错误处理和超时管理
4. 批量作业处理
"""

import asyncio
import json
from datetime import datetime
from financial_analysis.windmill_client import WindmillClient, windmill_client
from financial_analysis.config import settings


async def basic_job_example():
    """基本作业执行示例"""
    print("=== 基本作业执行示例 ===")
    
    # 创建客户端实例（也可以使用全局实例 windmill_client）
    client = WindmillClient()
    
    # 准备作业参数
    payload = {
        "message": "Hello from Python!",
        "timestamp": datetime.now().isoformat()
    }
    
    try:
        # 执行完整的作业流程
        result = await client.execute_job(
            folder="examples",
            script="hello_world",
            payload=payload,
            max_wait_time=60,  # 最大等待60秒
            poll_interval=2    # 每2秒轮询一次
        )
        
        if result:
            print(f"作业执行成功: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("作业执行失败")
            
    except Exception as e:
        print(f"执行过程中发生错误: {e}")


async def text_analysis_example():
    """文本分析生成示例"""
    print("\n=== 文本分析生成示例 ===")
    
    # 准备分析提示词
    prompt = """
    请分析以下股票的投资价值：
    
    股票信息：
    - 名称：苹果公司 (AAPL)
    - 当前价格：$150.25
    - 日涨跌：+2.5%
    - 市值：$2.4万亿
    - 行业：科技/消费电子
    
    技术指标：
    - RSI：65
    - 5日均线：$148.50
    - 20日均线：$145.20
    
    请提供技术分析、基本面分析和投资建议。
    """
    
    system_instruction = "你是一位专业的金融分析师，具有丰富的股票分析经验。"
    
    try:
        # 生成文本分析（启用搜索以获取最新市场信息）
        analysis = await windmill_client.generate_text_analysis(
            prompt=prompt,
            system_instruction=system_instruction,
            search=True  # 启用搜索功能以获取最新市场数据
        )
        
        if analysis:
            print(f"分析结果: {analysis}")
        else:
            print("分析生成失败")
            
    except Exception as e:
        print(f"分析过程中发生错误: {e}")


async def step_by_step_example():
    """分步执行示例"""
    print("\n=== 分步执行示例 ===")
    
    client = WindmillClient()
    
    # 准备参数
    payload = {"query": "Python异步编程最佳实践"}
    
    try:
        # 步骤1：触发作业
        print("步骤1：触发作业...")
        job_uuid = await client.trigger_job("research", "web_search", payload)
        
        if not job_uuid:
            print("作业触发失败")
            return
        
        print(f"作业已触发，UUID: {job_uuid}")
        
        # 步骤2：等待作业完成
        print("步骤2：等待作业完成...")
        result = await client.wait_for_job_completion(
            job_uuid=job_uuid,
            max_wait_time=120,
            poll_interval=3
        )
        
        if result:
            print(f"作业完成: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("作业执行失败或超时")
            
    except Exception as e:
        print(f"执行过程中发生错误: {e}")


async def batch_jobs_example():
    """批量作业处理示例"""
    print("\n=== 批量作业处理示例 ===")
    
    client = WindmillClient()
    
    # 准备多个作业
    jobs = [
        {"folder": "analysis", "script": "sentiment", "payload": {"text": "市场前景看好"}},
        {"folder": "analysis", "script": "sentiment", "payload": {"text": "经济形势严峻"}},
        {"folder": "analysis", "script": "sentiment", "payload": {"text": "投资需要谨慎"}},
    ]
    
    try:
        # 并发触发所有作业
        print("并发触发所有作业...")
        trigger_tasks = [
            client.trigger_job(job["folder"], job["script"], job["payload"])
            for job in jobs
        ]
        
        job_uuids = await asyncio.gather(*trigger_tasks, return_exceptions=True)
        
        # 过滤成功的作业UUID
        valid_uuids = [uuid for uuid in job_uuids if isinstance(uuid, str)]
        print(f"成功触发 {len(valid_uuids)} 个作业")
        
        if not valid_uuids:
            print("没有成功触发的作业")
            return
        
        # 并发等待所有作业完成
        print("等待所有作业完成...")
        completion_tasks = [
            client.wait_for_job_completion(uuid, max_wait_time=60, poll_interval=2)
            for uuid in valid_uuids
        ]
        
        results = await asyncio.gather(*completion_tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"作业 {i+1} 执行异常: {result}")
            elif result:
                print(f"作业 {i+1} 完成: {json.dumps(result, indent=2, ensure_ascii=False)}")
            else:
                print(f"作业 {i+1} 执行失败")
                
    except Exception as e:
        print(f"批量处理过程中发生错误: {e}")


async def error_handling_example():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    # 创建一个配置错误的客户端
    client = WindmillClient(
        base_url="https://invalid-url.com",
        token="invalid-token",
        workspace="invalid-workspace"
    )
    
    try:
        # 尝试执行作业（预期会失败）
        result = await client.execute_job(
            folder="test",
            script="test",
            payload={},
            max_wait_time=10
        )
        
        if result:
            print("意外成功")
        else:
            print("按预期失败：无效的配置导致作业执行失败")
            
    except Exception as e:
        print(f"捕获到异常: {e}")
    
    # 测试超时处理
    print("\n测试超时处理...")
    valid_client = WindmillClient()
    
    try:
        # 设置很短的超时时间
        result = await valid_client.wait_for_job_completion(
            job_uuid="non-existent-uuid",
            max_wait_time=2,  # 2秒超时
            poll_interval=0.5
        )
        
        if result:
            print("意外成功")
        else:
            print("按预期超时：作业等待超时")
            
    except Exception as e:
        print(f"捕获到异常: {e}")


async def configuration_example():
    """配置示例"""
    print("\n=== 配置示例 ===")
    
    # 显示当前配置
    print("当前 Windmill 配置:")
    print(f"  基础URL: {settings.windmill_base_url}")
    print(f"  工作空间: {settings.windmill_workspace}")
    print(f"  文件夹: {settings.windmill_folder}")
    print(f"  脚本: {settings.windmill_script}")
    print(f"  令牌: {'已配置' if settings.windmill_token else '未配置'}")
    
    # 创建自定义配置的客户端
    custom_client = WindmillClient(
        base_url="https://custom.windmill.com",
        token="custom_token",
        workspace="custom_workspace"
    )
    
    print(f"\n自定义客户端配置:")
    print(f"  基础URL: {custom_client.base_url}")
    print(f"  工作空间: {custom_client.workspace}")
    print(f"  令牌: {'已配置' if custom_client.token else '未配置'}")


async def main():
    """主函数"""
    print("Windmill 异步客户端使用示例")
    print("=" * 50)
    
    # 检查配置
    if not settings.windmill_base_url or not settings.windmill_token:
        print("警告: Windmill 配置不完整，某些示例可能无法正常运行")
        print("请在 .env 文件中配置以下变量:")
        print("  WINDMILL_BASE_URL=https://your-windmill-instance.com")
        print("  WINDMILL_TOKEN=your-access-token")
        print("  WINDMILL_WORKSPACE=your-workspace")
        print()
    
    # 运行各种示例
    await configuration_example()
    
    # 如果配置完整，运行功能示例
    if settings.windmill_base_url and settings.windmill_token:
        await basic_job_example()
        await text_analysis_example()
        await step_by_step_example()
        await batch_jobs_example()
    else:
        print("\n跳过功能示例（配置不完整）")
    
    # 错误处理示例总是运行
    await error_handling_example()
    
    print("\n示例执行完成！")


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
