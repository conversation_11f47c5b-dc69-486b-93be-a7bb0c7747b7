"""
动态数据源管理使用示例

演示如何使用新的动态数据源管理功能，包括：
1. 添加、删除、更新数据源
2. 使用统一的数据格式
3. 测试数据源连接
4. 收集和处理数据
"""

import json
from datetime import datetime
from financial_analysis.data_source_api import data_source_api
from financial_analysis.hot_news_collector import HotNewsCollector
from financial_analysis.models import DataSourceConfig


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 1. 列出现有数据源
    print("\n1. 列出现有数据源:")
    result = data_source_api.list_sources()
    if result["success"]:
        print(f"找到 {result['total']} 个数据源:")
        for source_data in result["data"]:
            config = source_data["config"]
            status = source_data["status"]
            print(f"  - {config['name']} ({config['source_id']}): "
                  f"类型={config['source_type']}, "
                  f"状态={'启用' if config['enabled'] else '禁用'}")
    
    # 2. 使用新系统收集数据
    print("\n2. 收集热点新闻:")
    collector = HotNewsCollector(use_new_system=True)
    news_items = collector.collect_unified_news(force_refresh=True)
    print(f"收集到 {len(news_items)} 条统一格式新闻")
    
    # 显示前3条新闻
    for i, news in enumerate(news_items[:3]):
        print(f"  新闻 {i+1}: {news.title[:50]}... (来源: {news.source_name})")


def example_add_custom_rss_source():
    """添加自定义RSS数据源示例"""
    print("\n=== 添加自定义RSS数据源示例 ===")
    
    # 定义新的RSS数据源
    rss_source = {
        "source_id": "custom_finance_rss",
        "name": "自定义财经RSS",
        "source_type": "rss",
        "adapter_class": "RSSAdapter",
        "config": {
            "url": "https://feeds.finance.yahoo.com/rss/2.0/headline"
        },
        "enabled": True,
        "priority": 10,
        "fetch_interval": 600
    }
    
    # 添加数据源
    print("添加RSS数据源...")
    result = data_source_api.create_source(rss_source)
    if result["success"]:
        print(f"✓ 成功添加数据源: {result['source_id']}")
        
        # 测试连接
        print("测试数据源连接...")
        test_result = data_source_api.test_source(rss_source["source_id"])
        if test_result["success"]:
            print(f"✓ 连接测试成功，获取到 {test_result['data_count']} 条数据")
        else:
            print(f"✗ 连接测试失败: {test_result['error']}")
    else:
        print(f"✗ 添加数据源失败: {result['error']}")


def example_add_custom_api_source():
    """添加自定义API数据源示例"""
    print("\n=== 添加自定义API数据源示例 ===")
    
    # 定义新的API数据源
    api_source = {
        "source_id": "custom_news_api",
        "name": "自定义新闻API",
        "source_type": "api",
        "adapter_class": "APIAdapter",
        "config": {
            "url": "https://newsapi.org/v2/top-headlines",
            "headers": {
                "X-API-Key": "your_api_key_here"  # 需要替换为真实的API密钥
            },
            "params": {
                "country": "us",
                "category": "business",
                "pageSize": 50
            },
            "field_mapping": {
                "title": ["title"],
                "content": ["description", "content"],
                "url": ["url"],
                "publish_time": ["publishedAt"],
                "author": ["author", "source.name"]
            },
            "data_path": ["articles"]
        },
        "enabled": False,  # 默认禁用，因为需要API密钥
        "priority": 5,
        "fetch_interval": 1800
    }
    
    # 添加数据源
    print("添加API数据源...")
    result = data_source_api.create_source(api_source)
    if result["success"]:
        print(f"✓ 成功添加数据源: {result['source_id']} (已禁用，需要配置API密钥)")
    else:
        print(f"✗ 添加数据源失败: {result['error']}")


def example_add_custom_web_source():
    """添加自定义Web爬虫数据源示例"""
    print("\n=== 添加自定义Web爬虫数据源示例 ===")
    
    # 定义新的Web数据源
    web_source = {
        "source_id": "custom_web_news",
        "name": "自定义网站新闻",
        "source_type": "web",
        "adapter_class": "WebAdapter",
        "config": {
            "url": "https://example-news-site.com/finance",
            "selectors": {
                "container": ".news-item, article",
                "title": "h2, h3, .title",
                "content": ".content, .summary, p",
                "link": "a",
                "time": ".time, .date, time"
            }
        },
        "enabled": False,  # 默认禁用，需要根据实际网站调整选择器
        "priority": 8,
        "fetch_interval": 3600
    }
    
    # 添加数据源
    print("添加Web数据源...")
    result = data_source_api.create_source(web_source)
    if result["success"]:
        print(f"✓ 成功添加数据源: {result['source_id']} (已禁用，需要调整选择器)")
    else:
        print(f"✗ 添加数据源失败: {result['error']}")


def example_manage_sources():
    """数据源管理示例"""
    print("\n=== 数据源管理示例 ===")
    
    # 1. 获取统计信息
    print("1. 获取统计信息:")
    stats = data_source_api.get_statistics()
    if stats["success"]:
        data = stats["data"]
        print(f"  总数据源: {data['total_sources']}")
        print(f"  启用数据源: {data['enabled_sources']}")
        print(f"  禁用数据源: {data['disabled_sources']}")
        print(f"  类型分布: {data['type_distribution']}")
    
    # 2. 更新数据源配置
    print("\n2. 更新数据源配置:")
    sources = data_source_api.list_sources()
    if sources["success"] and sources["data"]:
        first_source_id = sources["data"][0]["config"]["source_id"]
        
        # 更新获取间隔
        update_result = data_source_api.update_source(first_source_id, {
            "fetch_interval": 900  # 改为15分钟
        })
        
        if update_result["success"]:
            print(f"✓ 成功更新数据源 {first_source_id} 的获取间隔")
        else:
            print(f"✗ 更新失败: {update_result['error']}")
    
    # 3. 启用/禁用数据源
    print("\n3. 启用/禁用数据源:")
    if sources["success"] and len(sources["data"]) > 1:
        second_source_id = sources["data"][1]["config"]["source_id"]
        
        # 禁用数据源
        disable_result = data_source_api.disable_source(second_source_id)
        if disable_result["success"]:
            print(f"✓ 成功禁用数据源 {second_source_id}")
            
            # 重新启用
            enable_result = data_source_api.enable_source(second_source_id)
            if enable_result["success"]:
                print(f"✓ 成功重新启用数据源 {second_source_id}")


def example_install_predefined_sources():
    """安装预定义数据源示例"""
    print("\n=== 安装预定义数据源示例 ===")
    
    # 1. 获取预定义数据源列表
    print("1. 获取预定义数据源:")
    predefined = data_source_api.get_predefined_sources()
    if predefined["success"]:
        print(f"找到 {predefined['total']} 个预定义数据源:")
        for source in predefined["data"]:
            print(f"  - {source['name']} ({source['source_id']}): {source['source_type']}")
    
    # 2. 安装一个预定义数据源
    if predefined["success"] and predefined["data"]:
        source_to_install = predefined["data"][0]["source_id"]
        print(f"\n2. 安装预定义数据源: {source_to_install}")
        
        install_result = data_source_api.install_predefined_source(source_to_install)
        if install_result["success"]:
            print(f"✓ 成功安装预定义数据源: {source_to_install}")
        else:
            print(f"✗ 安装失败: {install_result['error']}")


def example_collect_and_analyze():
    """收集和分析数据示例"""
    print("\n=== 收集和分析数据示例 ===")
    
    # 1. 从所有数据源收集数据
    print("1. 从所有数据源收集数据:")
    collect_result = data_source_api.collect_all_sources(force_refresh=True)
    
    if collect_result["success"]:
        data = collect_result["data"]
        print(f"✓ 成功收集到 {collect_result['count']} 条数据")
        print(f"  处理时间: {collect_result['processing_time']:.2f} 秒")
        
        # 2. 分析数据分布
        print("\n2. 数据分布分析:")
        source_stats = {}
        category_stats = {}
        
        for item in data:
            # 统计来源分布
            source = item["source_name"]
            source_stats[source] = source_stats.get(source, 0) + 1
            
            # 统计分类分布
            category = item.get("category", "未分类")
            category_stats[category] = category_stats.get(category, 0) + 1
        
        print("  来源分布:")
        for source, count in sorted(source_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"    {source}: {count} 条")
        
        print("  分类分布:")
        for category, count in sorted(category_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"    {category}: {count} 条")
        
        # 3. 显示最新的几条新闻
        print("\n3. 最新新闻预览:")
        sorted_data = sorted(data, 
                           key=lambda x: x.get("publish_time") or x.get("fetch_time"), 
                           reverse=True)
        
        for i, item in enumerate(sorted_data[:5]):
            print(f"  {i+1}. {item['title'][:60]}...")
            print(f"     来源: {item['source_name']} | 时间: {item.get('publish_time', 'N/A')}")
    
    else:
        print(f"✗ 收集数据失败: {collect_result['error']}")


def main():
    """主函数"""
    print("动态数据源管理功能演示")
    print("=" * 50)
    
    try:
        # 基础使用
        example_basic_usage()
        
        # 添加不同类型的数据源
        example_add_custom_rss_source()
        example_add_custom_api_source()
        example_add_custom_web_source()
        
        # 数据源管理
        example_manage_sources()
        
        # 安装预定义数据源
        example_install_predefined_sources()
        
        # 收集和分析数据
        example_collect_and_analyze()
        
        print("\n" + "=" * 50)
        print("演示完成！")
        
    except Exception as e:
        print(f"演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
