#!/usr/bin/env python3
"""
真实数据验证脚本

验证项目是否正确获取真实的金融数据，并提供详细的数据质量报告。
"""

import sys
import os
from datetime import datetime, timedelta
import json

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_stock_data_quality():
    """验证股票数据质量"""
    print("=" * 60)
    print("股票数据质量验证")
    print("=" * 60)
    
    try:
        from financial_analysis import StockDataProvider
        provider = StockDataProvider()
        
        # 测试多个股票的数据质量
        test_stocks = [
            ("000001", "SZSE", "平安银行"),
            ("000002", "SZSE", "万科A"),
            ("600036", "SSE", "招商银行"),
            ("AAPL", "NASDAQ", "苹果"),
            ("MSFT", "NASDAQ", "微软")
        ]
        
        real_data_count = 0
        total_tests = len(test_stocks)
        
        for symbol, exchange, name in test_stocks:
            print(f"\n📊 测试 {symbol} ({name})...")
            
            try:
                # 获取股票信息
                stock_info = provider.get_stock_info(symbol, exchange)
                if not stock_info:
                    print(f"  ✗ 无法获取股票信息")
                    continue
                
                # 获取价格数据
                prices = provider.get_stock_prices(symbol, days=5, exchange=exchange)
                if not prices or len(prices) < 2:
                    print(f"  ✗ 价格数据不足")
                    continue
                
                # 数据质量检查
                latest_price = prices[-1].close_price
                previous_price = prices[-2].close_price
                price_change = abs(latest_price - previous_price)
                
                # 检查价格合理性
                is_reasonable_price = True
                if exchange in ["SZSE", "SSE"]:  # A股
                    is_reasonable_price = 1 <= latest_price <= 500
                else:  # 美股
                    is_reasonable_price = 1 <= latest_price <= 1000
                
                # 检查价格变化合理性
                price_change_percent = (price_change / previous_price) * 100
                is_reasonable_change = price_change_percent <= 20  # 单日涨跌幅不超过20%
                
                # 检查数据时效性
                latest_date = prices[-1].date
                is_recent = (datetime.now().date() - latest_date).days <= 7
                
                # 综合判断
                if is_reasonable_price and is_reasonable_change and is_recent:
                    print(f"  ✓ 真实数据 - 价格: {latest_price:.2f}, 变化: {price_change_percent:+.2f}%")
                    real_data_count += 1
                else:
                    print(f"  ⚠️ 疑似模拟数据 - 价格: {latest_price:.2f}")
                    if not is_reasonable_price:
                        print(f"    - 价格异常: {latest_price}")
                    if not is_reasonable_change:
                        print(f"    - 变化异常: {price_change_percent:.2f}%")
                    if not is_recent:
                        print(f"    - 数据过期: {latest_date}")
                
            except Exception as e:
                print(f"  ✗ 测试失败: {str(e)}")
        
        # 汇总结果
        real_data_ratio = real_data_count / total_tests
        print(f"\n📈 股票数据质量汇总:")
        print(f"  真实数据: {real_data_count}/{total_tests} ({real_data_ratio*100:.1f}%)")
        
        if real_data_ratio >= 0.8:
            print("  ✅ 股票数据质量优秀")
        elif real_data_ratio >= 0.5:
            print("  ⚠️ 股票数据质量一般")
        else:
            print("  ❌ 股票数据质量较差，建议检查配置")
            
        return real_data_ratio
        
    except Exception as e:
        print(f"❌ 股票数据验证失败: {str(e)}")
        return 0

def verify_news_data_quality():
    """验证新闻数据质量"""
    print("\n" + "=" * 60)
    print("新闻数据质量验证")
    print("=" * 60)
    
    try:
        from financial_analysis import NewsSearcher
        from financial_analysis.models import StockInfo
        
        searcher = NewsSearcher()
        
        # 创建测试股票
        test_stock = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE",
            currency="CNY",
            sector="银行",
            market_cap=150000000000
        )
        
        print(f"🔍 搜索 {test_stock.name} 相关新闻...")
        
        news_items = searcher.search_stock_news(test_stock, days=7)
        
        if not news_items:
            print("  ✗ 未获取到新闻数据")
            return 0
        
        print(f"  📰 获取到 {len(news_items)} 条新闻")
        
        # 检查新闻质量
        mock_indicators = [
            "发布最新财报，业绩超预期",
            "市场分析师看好",
            "面临行业监管政策调整压力"
        ]
        
        mock_news_count = 0
        for news in news_items:
            is_mock = any(indicator in news.title for indicator in mock_indicators)
            if is_mock:
                mock_news_count += 1
        
        real_news_ratio = 1 - (mock_news_count / len(news_items))
        
        print(f"  📊 新闻质量分析:")
        print(f"    真实新闻: {len(news_items) - mock_news_count}/{len(news_items)}")
        print(f"    模拟新闻: {mock_news_count}/{len(news_items)}")
        
        # 显示前3条新闻标题
        print(f"  📋 新闻样本:")
        for i, news in enumerate(news_items[:3], 1):
            status = "🔴 模拟" if any(indicator in news.title for indicator in mock_indicators) else "🟢 真实"
            print(f"    {i}. {status} {news.title[:50]}...")
        
        if real_news_ratio >= 0.8:
            print("  ✅ 新闻数据质量优秀")
        elif real_news_ratio >= 0.3:
            print("  ⚠️ 新闻数据部分真实")
        else:
            print("  ❌ 新闻数据主要为模拟数据")
            print("  💡 建议检查Windmill配置")
        
        return real_news_ratio
        
    except Exception as e:
        print(f"❌ 新闻数据验证失败: {str(e)}")
        return 0

def verify_ai_analysis_quality():
    """验证AI分析质量"""
    print("\n" + "=" * 60)
    print("AI分析质量验证")
    print("=" * 60)
    
    try:
        from financial_analysis import AnalysisEngine
        
        engine = AnalysisEngine()
        
        print("🤖 生成AI分析报告...")
        report = engine.generate_analysis_report("000001", "SZSE")
        
        if not report:
            print("  ✗ 无法生成分析报告")
            return 0
        
        # 检查AI分析质量
        ai_analysis = report.ai_analysis
        
        if not ai_analysis:
            print("  ✗ 未获取到AI分析内容")
            return 0
        
        # 分析内容质量指标
        analysis_length = len(ai_analysis)
        has_technical_terms = any(term in ai_analysis for term in ["技术面", "基本面", "RSI", "MACD", "均线"])
        has_specific_data = any(char.isdigit() for char in ai_analysis)
        
        print(f"  📝 AI分析内容长度: {analysis_length} 字符")
        print(f"  🔍 包含技术术语: {'是' if has_technical_terms else '否'}")
        print(f"  📊 包含具体数据: {'是' if has_specific_data else '否'}")
        
        # 显示分析摘要
        analysis_preview = ai_analysis[:200] + "..." if len(ai_analysis) > 200 else ai_analysis
        print(f"  📄 分析摘要: {analysis_preview}")
        
        # 质量评分
        quality_score = 0
        if analysis_length > 100:
            quality_score += 0.4
        if has_technical_terms:
            quality_score += 0.3
        if has_specific_data:
            quality_score += 0.3
        
        if quality_score >= 0.8:
            print("  ✅ AI分析质量优秀")
        elif quality_score >= 0.5:
            print("  ⚠️ AI分析质量一般")
        else:
            print("  ❌ AI分析质量较差")
            print("  💡 可能使用了简化版本，建议检查Windmill配置")
        
        return quality_score
        
    except Exception as e:
        print(f"❌ AI分析验证失败: {str(e)}")
        return 0

def generate_quality_report(stock_score, news_score, ai_score):
    """生成数据质量报告"""
    print("\n" + "=" * 60)
    print("数据质量综合报告")
    print("=" * 60)
    
    overall_score = (stock_score + news_score + ai_score) / 3
    
    print(f"\n📊 各模块质量评分:")
    print(f"  股票数据: {stock_score*100:.1f}%")
    print(f"  新闻数据: {news_score*100:.1f}%")
    print(f"  AI分析:   {ai_score*100:.1f}%")
    print(f"  综合评分: {overall_score*100:.1f}%")
    
    # 生成建议
    print(f"\n💡 改进建议:")
    
    if stock_score < 0.8:
        print("  📈 股票数据:")
        print("    - 检查akshare和yfinance是否正确安装")
        print("    - 确认网络连接正常")
        print("    - 检查是否达到API调用限制")
    
    if news_score < 0.8:
        print("  📰 新闻数据:")
        print("    - 检查Windmill配置是否正确")
        print("    - 确认token具有必要权限")
        print("    - 验证脚本路径是否正确")
    
    if ai_score < 0.8:
        print("  🤖 AI分析:")
        print("    - 检查Windmill AI接口配置")
        print("    - 确认大语言模型服务可用")
        print("    - 检查网络连接和权限")
    
    # 总体评价
    if overall_score >= 0.8:
        print(f"\n🎉 恭喜！您的项目已成功配置为使用真实金融数据")
        print(f"   数据质量优秀，可以用于实际分析")
    elif overall_score >= 0.5:
        print(f"\n⚠️ 您的项目部分使用真实数据")
        print(f"   建议根据上述建议进行优化")
    else:
        print(f"\n❌ 您的项目主要使用模拟数据")
        print(f"   请按照建议进行配置修复")
    
    # 保存报告
    report_data = {
        "timestamp": datetime.now().isoformat(),
        "scores": {
            "stock_data": stock_score,
            "news_data": news_score,
            "ai_analysis": ai_score,
            "overall": overall_score
        }
    }
    
    with open("data_quality_report.json", "w", encoding="utf-8") as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存至: data_quality_report.json")

def main():
    """主函数"""
    print("🔍 金融数据质量验证")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 验证各模块数据质量
        stock_score = verify_stock_data_quality()
        news_score = verify_news_data_quality()
        ai_score = verify_ai_analysis_quality()
        
        # 生成综合报告
        generate_quality_report(stock_score, news_score, ai_score)
        
    except KeyboardInterrupt:
        print("\n\n❌ 验证被用户中断")
    except Exception as e:
        print(f"\n\n❌ 验证过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
