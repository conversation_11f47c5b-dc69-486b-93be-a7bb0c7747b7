#!/usr/bin/env python3
"""
RSS解析修复测试脚本

测试修复后的RSS数据源是否能正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from financial_analysis.data_source_registry import data_source_registry
from financial_analysis.data_source_adapters import get_adapter_class
from financial_analysis.utils import setup_logging


def test_rss_sources():
    """测试RSS数据源"""
    print("🔍 测试RSS数据源修复效果")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    
    # 获取所有数据源
    sources = data_source_registry.list_sources()

    print(f"📊 总共有 {len(sources)} 个数据源")
    print()

    # 测试每个启用的RSS数据源
    for source_config in sources:
        if not source_config.enabled:
            print(f"⏭️  跳过禁用的数据源: {source_config.name}")
            continue
            
        if source_config.source_type != 'rss':
            print(f"⏭️  跳过非RSS数据源: {source_config.name} ({source_config.source_type})")
            continue
            
        print(f"🧪 测试RSS数据源: {source_config.name}")
        print(f"   URL: {source_config.config.get('url')}")
        
        try:
            # 创建适配器
            adapter_class = get_adapter_class(source_config.source_type)
            adapter = adapter_class(source_config)
            
            # 验证配置
            if not adapter.validate_config():
                print(f"   ❌ 配置验证失败")
                continue
                
            # 获取数据
            news_data = adapter.fetch_data()
            
            if news_data:
                print(f"   ✅ 成功获取 {len(news_data)} 条新闻")
                
                # 显示前3条新闻的标题
                for i, news in enumerate(news_data[:3], 1):
                    print(f"   [{i}] {news.title}")
                    
                if len(news_data) > 3:
                    print(f"   ... 还有 {len(news_data) - 3} 条新闻")
            else:
                print(f"   ⚠️  没有获取到新闻数据")
                
        except Exception as e:
            print(f"   ❌ 获取失败: {str(e)}")
            
        print()


def test_specific_rss_url(url: str, name: str):
    """测试特定的RSS URL"""
    print(f"🧪 测试RSS URL: {name}")
    print(f"   URL: {url}")
    
    try:
        import feedparser
        
        # 解析RSS
        feed = feedparser.parse(url)
        
        if feed.bozo:
            print(f"   ⚠️  RSS解析警告: {feed.bozo_exception}")
        
        if hasattr(feed, 'entries') and feed.entries:
            print(f"   ✅ 成功解析，获取到 {len(feed.entries)} 条条目")
            
            # 显示前3条的标题
            for i, entry in enumerate(feed.entries[:3], 1):
                title = getattr(entry, 'title', '无标题')
                print(f"   [{i}] {title}")
                
            if len(feed.entries) > 3:
                print(f"   ... 还有 {len(feed.entries) - 3} 条条目")
        else:
            print(f"   ❌ 没有获取到RSS条目")
            
    except Exception as e:
        print(f"   ❌ RSS解析失败: {str(e)}")
        
    print()


def main():
    """主函数"""
    print("🔧 RSS解析修复测试")
    print("=" * 60)
    
    # 测试配置的数据源
    test_rss_sources()
    
    print("🌐 测试其他RSS源")
    print("=" * 60)
    
    # 测试一些其他的RSS源
    test_rss_urls = [
        ("https://www.ftchinese.com/rss/feed", "FT中文网"),
        ("https://feeds.bbci.co.uk/news/rss.xml", "BBC新闻"),
        ("https://feeds.reuters.com/reuters/topNews", "路透社头条"),
    ]
    
    for url, name in test_rss_urls:
        test_specific_rss_url(url, name)
    
    print("✅ RSS解析测试完成")


if __name__ == "__main__":
    main()
