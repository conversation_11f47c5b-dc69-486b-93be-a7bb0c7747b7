#!/usr/bin/env python3
"""
数据源测试脚本

用于验证各个数据源是否正常工作，区分真实数据和模拟数据。
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis import StockDataProvider, NewsSearcher, AnalysisEngine
from financial_analysis.config import settings
from financial_analysis.utils import setup_logging


def test_stock_data():
    """测试股票数据源"""
    print("=" * 60)
    print("股票数据源测试")
    print("=" * 60)
    
    provider = StockDataProvider()
    
    # 测试中国A股数据
    print("\n1. 测试中国A股数据 (000001 平安银行)")
    try:
        stock_info = provider.get_stock_info("000001", "SZSE")
        if stock_info:
            print(f"✓ 股票信息获取成功: {stock_info.name}")
            print(f"  交易所: {stock_info.exchange}")
            print(f"  行业: {stock_info.sector}")
            print(f"  市值: {stock_info.market_cap:,.0f}")
            
            # 获取价格数据
            prices = provider.get_stock_prices("000001", days=5, exchange="SZSE")
            if prices and len(prices) >= 2:
                latest = prices[-1]
                previous = prices[-2]
                print(f"  最新价格: {latest.close_price:.2f}")
                print(f"  前一日价格: {previous.close_price:.2f}")
                print(f"  价格变化: {latest.close_price - previous.close_price:+.2f}")
                
                # 判断数据类型
                if abs(latest.close_price - 12.0) < 1.0 and len(set(p.close_price for p in prices)) < 3:
                    print("  ⚠️  疑似模拟数据（价格变化模式规律）")
                else:
                    print("  ✓ 真实市场数据")
            else:
                print("  ✗ 价格数据获取失败")
        else:
            print("  ✗ 股票信息获取失败")
            
    except Exception as e:
        print(f"  ✗ 测试失败: {str(e)}")
    
    # 测试美股数据
    print("\n2. 测试美股数据 (AAPL)")
    try:
        stock_info = provider.get_stock_info("AAPL", "NASDAQ")
        if stock_info:
            print(f"✓ 股票信息获取成功: {stock_info.name}")
            prices = provider.get_stock_prices("AAPL", days=3, exchange="NASDAQ")
            if prices:
                print(f"  最新价格: {prices[-1].close_price:.2f} {stock_info.currency}")
                print("  ✓ 国际股票数据正常")
            else:
                print("  ✗ 价格数据获取失败")
        else:
            print("  ✗ 股票信息获取失败")
            
    except Exception as e:
        print(f"  ✗ 测试失败: {str(e)}")


def test_news_data():
    """测试新闻数据源"""
    print("\n" + "=" * 60)
    print("新闻数据源测试")
    print("=" * 60)
    
    searcher = NewsSearcher()
    
    # 创建测试股票信息
    from financial_analysis.models import StockInfo
    test_stock = StockInfo(
        symbol="000001",
        name="平安银行",
        exchange="SZSE",
        currency="CNY",
        sector="银行",
        market_cap=150000000000
    )
    
    print(f"\n测试股票: {test_stock.name} ({test_stock.symbol})")
    
    try:
        # 搜索新闻
        news_items = searcher.search_stock_news(test_stock, days=7)
        print(f"获取到 {len(news_items)} 条新闻")
        
        if news_items:
            # 显示前3条新闻标题
            for i, news in enumerate(news_items[:3], 1):
                print(f"  {i}. {news.title}")
                print(f"     来源: {news.source} | 情感: {news.sentiment}")
            
            # 判断数据类型
            mock_titles = [
                "发布最新财报，业绩超预期",
                "市场分析师看好",
                "面临行业监管政策调整压力"
            ]
            
            is_mock = any(any(mock_part in news.title for mock_part in mock_titles) 
                         for news in news_items)
            
            if is_mock:
                print("  ⚠️  检测到模拟新闻数据")
                print("  原因: Windmill配置问题或API调用失败")
            else:
                print("  ✓ 真实新闻数据")
                
            # 测试情感分析
            sentiment_result = searcher.analyze_news_sentiment(news_items)
            print(f"\n情感分析结果:")
            print(f"  整体情感: {sentiment_result['overall_sentiment']}")
            print(f"  正面: {sentiment_result['positive_count']} | "
                  f"负面: {sentiment_result['negative_count']} | "
                  f"中性: {sentiment_result['neutral_count']}")
                  
        else:
            print("  ✗ 未获取到新闻数据")
            
    except Exception as e:
        print(f"  ✗ 新闻测试失败: {str(e)}")


def test_ai_analysis():
    """测试AI分析功能"""
    print("\n" + "=" * 60)
    print("AI分析功能测试")
    print("=" * 60)
    
    engine = AnalysisEngine()
    
    print("\n测试完整分析报告生成...")
    try:
        report = engine.generate_analysis_report("000001", "SZSE")
        
        if report:
            print("✓ 分析报告生成成功")
            print(f"  股票: {report.stock_info.name}")
            print(f"  当前价格: {report.current_price:.2f}")
            print(f"  综合评级: {report.overall_rating}")
            print(f"  投资建议: {report.investment_advice[:50]}...")
            
            # 检查AI分析质量
            if report.ai_analysis and len(report.ai_analysis) > 200:
                print("  ✓ AI分析内容丰富")
            else:
                print("  ⚠️  AI分析内容简化（可能使用了降级方案）")
                
        else:
            print("  ✗ 分析报告生成失败")
            
    except Exception as e:
        print(f"  ✗ AI分析测试失败: {str(e)}")


def test_configuration():
    """测试配置状态"""
    print("\n" + "=" * 60)
    print("配置状态检查")
    print("=" * 60)
    
    print(f"\n当前配置:")
    print(f"  Windmill URL: {settings.windmill_base_url}")
    print(f"  Windmill Token: {'已配置' if settings.windmill_token else '未配置'}")
    print(f"  工作空间: {settings.windmill_workspace}")
    print(f"  脚本路径: {settings.windmill_folder}/{settings.windmill_script}")
    
    # 检查依赖
    print(f"\n依赖检查:")
    try:
        import akshare
        print("  ✓ akshare 已安装")
    except ImportError:
        print("  ✗ akshare 未安装")
    
    try:
        import yfinance
        print("  ✓ yfinance 已安装")
    except ImportError:
        print("  ✗ yfinance 未安装")
    
    try:
        import aiohttp
        print("  ✓ aiohttp 已安装")
    except ImportError:
        print("  ✗ aiohttp 未安装")


def main():
    """主函数"""
    print(f"数据源测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置日志
    setup_logging()
    
    # 运行各项测试
    test_configuration()
    test_stock_data()
    test_news_data()
    test_ai_analysis()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("\n如果发现模拟数据问题，请参考 docs/troubleshooting.md")


if __name__ == "__main__":
    main()
