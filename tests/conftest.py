"""
pytest配置文件

提供测试的全局配置和fixture。
"""

import pytest
import os
from unittest.mock import patch
from datetime import datetime

from financial_analysis.config import Settings


@pytest.fixture
def mock_settings():
    """模拟配置fixture"""
    with patch('financial_analysis.config.settings') as mock:
        mock.gemini_model = "gemini-pro"
        mock.windmill_base_url = "https://wm.atjog.com"
        mock.windmill_token = "test_token"
        mock.windmill_workspace = "my-workspace"
        mock.windmill_folder = "gemini"
        mock.windmill_script = "text_generation"
        mock.log_level = "INFO"
        mock.log_file = "test.log"
        mock.default_exchange = "SSE"
        mock.data_cache_duration = 300
        mock.analysis_days = 30
        mock.news_search_days = 7
        mock.hot_news_history_check_days = 3
        yield mock


@pytest.fixture
def sample_stock_info():
    """示例股票信息fixture"""
    from financial_analysis.models import StockInfo
    return StockInfo(
        symbol="000001",
        name="平安银行",
        exchange="SZSE",
        currency="CNY",
        sector="银行业",
        market_cap=150000000000
    )


@pytest.fixture
def sample_stock_prices():
    """示例股票价格数据fixture"""
    from financial_analysis.models import StockPrice
    prices = []
    for i in range(30):
        price = StockPrice(
            symbol="000001",
            date=datetime(2024, 1, 1 + i),
            open_price=10.0 + i * 0.1,
            high_price=10.5 + i * 0.1,
            low_price=9.5 + i * 0.1,
            close_price=10.0 + i * 0.1,
            volume=1000000 + i * 10000
        )
        prices.append(price)
    return prices


@pytest.fixture
def sample_news_items():
    """示例新闻数据fixture"""
    from financial_analysis.models import NewsItem
    return [
        NewsItem(
            title="平安银行发布三季度财报",
            content="平安银行业绩超预期",
            source="财经新闻网",
            publish_time=datetime(2024, 1, 15, 10, 0),
            sentiment="positive"
        ),
        NewsItem(
            title="银行业监管政策调整",
            content="新政策对银行业影响分析",
            source="监管快讯",
            publish_time=datetime(2024, 1, 15, 14, 0),
            sentiment="negative"
        ),
        NewsItem(
            title="市场分析师观点",
            content="分析师对银行股的看法",
            source="投资者报",
            publish_time=datetime(2024, 1, 15, 16, 0),
            sentiment="neutral"
        )
    ]


@pytest.fixture
def sample_technical_indicators():
    """示例技术指标fixture"""
    from financial_analysis.models import TechnicalIndicators
    return TechnicalIndicators(
        symbol="000001",
        date=datetime(2024, 1, 15),
        ma5=12.50,
        ma10=12.40,
        ma20=12.30,
        ma60=12.00,
        rsi=65.5,
        macd=0.15,
        kdj_k=75.2,
        kdj_d=68.9,
        kdj_j=87.8
    )


@pytest.fixture(autouse=True)
def setup_test_environment():
    """自动设置测试环境"""
    # 设置测试环境变量
    os.environ['TESTING'] = 'true'
    
    # 禁用日志输出到文件
    with patch('financial_analysis.utils.setup_logging'):
        yield
    
    # 清理环境变量
    if 'TESTING' in os.environ:
        del os.environ['TESTING']


@pytest.fixture
def mock_requests_post():
    """模拟requests.post的fixture"""
    with patch('requests.post') as mock:
        yield mock


@pytest.fixture
def mock_akshare():
    """模拟akshare的fixture"""
    with patch('financial_analysis.stock_data.ak') as mock:
        yield mock


@pytest.fixture
def mock_yfinance():
    """模拟yfinance的fixture"""
    with patch('financial_analysis.stock_data.yf') as mock:
        yield mock
