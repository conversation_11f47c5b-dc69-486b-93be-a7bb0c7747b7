"""
新闻搜索模块测试

测试新闻搜索、情感分析和摘要生成功能。
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import requests

from financial_analysis.news_search import NewsSearcher
from financial_analysis.models import StockInfo, NewsItem


class TestNewsSearcher:
    """测试NewsSearcher类"""
    
    def setup_method(self):
        """测试前准备"""
        self.searcher = NewsSearcher()
        self.stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE",
            sector="银行业"
        )
    
    def test_init(self):
        """测试初始化"""
        assert self.searcher._cache == {}
        assert self.searcher._cache_timeout > 0
    
    def test_build_search_query(self):
        """测试构造搜索查询"""
        query = self.searcher._build_search_query(self.stock_info, 7)
        
        assert "平安银行" in query
        assert "000001" in query
        assert "银行业" in query
        assert "搜索关于" in query
        assert "时间范围" in query
    
    def test_build_search_query_no_sector(self):
        """测试无行业信息时的搜索查询构造"""
        stock_info_no_sector = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
        
        query = self.searcher._build_search_query(stock_info_no_sector, 7)
        
        assert "平安银行" in query
        assert "000001" in query
        assert "银行业" not in query


class TestNewsSentimentAnalysis:
    """测试新闻情感分析"""
    
    def setup_method(self):
        """测试前准备"""
        self.searcher = NewsSearcher()
    
    def test_analyze_news_sentiment_empty_list(self):
        """测试空新闻列表的情感分析"""
        result = self.searcher.analyze_news_sentiment([])
        
        expected = {
            'overall_sentiment': 'neutral',
            'positive_count': 0,
            'negative_count': 0,
            'neutral_count': 0,
            'sentiment_score': 0.0
        }
        
        assert result == expected
    
    def test_analyze_news_sentiment_positive(self):
        """测试正面新闻的情感分析"""
        news_items = [
            NewsItem(
                title="好消息1",
                source="来源1",
                publish_time=datetime.now(),
                sentiment="positive"
            ),
            NewsItem(
                title="好消息2",
                source="来源2",
                publish_time=datetime.now(),
                sentiment="positive"
            ),
            NewsItem(
                title="中性消息",
                source="来源3",
                publish_time=datetime.now(),
                sentiment="neutral"
            )
        ]
        
        result = self.searcher.analyze_news_sentiment(news_items)
        
        assert result['overall_sentiment'] == 'positive'
        assert result['positive_count'] == 2
        assert result['negative_count'] == 0
        assert result['neutral_count'] == 1
        assert result['sentiment_score'] > 0.2
    
    def test_analyze_news_sentiment_negative(self):
        """测试负面新闻的情感分析"""
        news_items = [
            NewsItem(
                title="坏消息1",
                source="来源1",
                publish_time=datetime.now(),
                sentiment="negative"
            ),
            NewsItem(
                title="坏消息2",
                source="来源2",
                publish_time=datetime.now(),
                sentiment="negative"
            ),
            NewsItem(
                title="中性消息",
                source="来源3",
                publish_time=datetime.now(),
                sentiment="neutral"
            )
        ]
        
        result = self.searcher.analyze_news_sentiment(news_items)
        
        assert result['overall_sentiment'] == 'negative'
        assert result['positive_count'] == 0
        assert result['negative_count'] == 2
        assert result['neutral_count'] == 1
        assert result['sentiment_score'] < -0.2
    
    def test_analyze_news_sentiment_neutral(self):
        """测试中性新闻的情感分析"""
        news_items = [
            NewsItem(
                title="好消息",
                source="来源1",
                publish_time=datetime.now(),
                sentiment="positive"
            ),
            NewsItem(
                title="坏消息",
                source="来源2",
                publish_time=datetime.now(),
                sentiment="negative"
            ),
            NewsItem(
                title="中性消息",
                source="来源3",
                publish_time=datetime.now(),
                sentiment="neutral"
            )
        ]
        
        result = self.searcher.analyze_news_sentiment(news_items)
        
        assert result['overall_sentiment'] == 'neutral'
        assert result['positive_count'] == 1
        assert result['negative_count'] == 1
        assert result['neutral_count'] == 1
        assert -0.2 <= result['sentiment_score'] <= 0.2


class TestWindmillIntegration:
    """测试Windmill集成"""
    
    def setup_method(self):
        """测试前准备"""
        self.searcher = NewsSearcher()
        self.stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
    
    @patch('financial_analysis.news_search.requests.post')
    @patch('financial_analysis.news_search.settings')
    def test_search_via_windmill_success(self, mock_settings, mock_post):
        """测试成功通过Windmill搜索新闻"""
        # 模拟配置
        mock_settings.windmill_base_url = "http://test.com"
        mock_settings.windmill_token = "test_token"
        mock_settings.windmill_workspace = "test_workspace"
        mock_settings.windmill_folder = "test_folder"
        mock_settings.windmill_script = "test_script"
        
        # 模拟API响应
        mock_response = Mock()
        mock_response.json.return_value = {
            "news_items": [
                {
                    "title": "测试新闻1",
                    "content": "测试内容1",
                    "source": "测试来源1",
                    "publish_time": "2024-01-15T10:00:00Z",
                    "url": "http://test1.com",
                    "sentiment": "positive"
                },
                {
                    "title": "测试新闻2",
                    "source": "测试来源2",
                    "publish_time": "2024-01-15T11:00:00Z",
                    "sentiment": "negative"
                }
            ]
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # 调用方法
        result = self.searcher._search_via_windmill("test query", self.stock_info)
        
        # 验证结果
        assert len(result) == 2
        assert all(isinstance(item, NewsItem) for item in result)
        
        first_news = result[0]
        assert first_news.title == "测试新闻1"
        assert first_news.sentiment == "positive"
        
        # 验证API调用
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert "Authorization" in call_args[1]["headers"]
        assert "Bearer test_token" in call_args[1]["headers"]["Authorization"]
    
    @patch('financial_analysis.news_search.requests.post')
    @patch('financial_analysis.news_search.settings')
    def test_search_via_windmill_api_error(self, mock_settings, mock_post):
        """测试Windmill API错误处理"""
        # 模拟配置
        mock_settings.windmill_base_url = "http://test.com"
        mock_settings.windmill_token = "test_token"
        mock_settings.windmill_workspace = "test_workspace"
        mock_settings.windmill_folder = "test_folder"
        mock_settings.windmill_script = "test_script"
        
        # 模拟API错误
        mock_post.side_effect = requests.RequestException("Network error")
        
        # 调用方法
        with patch.object(self.searcher, '_generate_mock_news') as mock_generate_mock:
            mock_generate_mock.return_value = []
            result = self.searcher._search_via_windmill("test query", self.stock_info)
            
            # 验证降级到模拟数据
            mock_generate_mock.assert_called_once_with(self.stock_info)
    
    @patch('financial_analysis.news_search.settings')
    def test_search_via_windmill_no_config(self, mock_settings):
        """测试Windmill配置不完整"""
        # 模拟配置不完整
        mock_settings.windmill_base_url = None
        mock_settings.windmill_token = None
        
        # 调用方法
        with patch.object(self.searcher, '_generate_mock_news') as mock_generate_mock:
            mock_generate_mock.return_value = []
            result = self.searcher._search_via_windmill("test query", self.stock_info)
            
            # 验证降级到模拟数据
            mock_generate_mock.assert_called_once_with(self.stock_info)


class TestNewsSummaryGeneration:
    """测试新闻摘要生成"""
    
    def setup_method(self):
        """测试前准备"""
        self.searcher = NewsSearcher()
        self.stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
    
    def test_generate_news_summary_empty_list(self):
        """测试空新闻列表的摘要生成"""
        result = self.searcher.generate_news_summary([], self.stock_info)
        
        assert "未找到关于" in result
        assert "平安银行" in result
        assert "000001" in result
    
    @patch('financial_analysis.news_search.requests.post')
    @patch('financial_analysis.news_search.settings')
    def test_generate_summary_via_windmill_success(self, mock_settings, mock_post):
        """测试成功通过Windmill生成摘要"""
        # 模拟配置
        mock_settings.windmill_base_url = "http://test.com"
        mock_settings.windmill_token = "test_token"
        mock_settings.windmill_workspace = "test_workspace"
        mock_settings.windmill_folder = "test_folder"
        mock_settings.windmill_script = "test_script"
        
        # 模拟API响应
        mock_response = Mock()
        mock_response.json.return_value = {
            "summary": "这是生成的新闻摘要"
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # 准备新闻内容
        news_content = ["新闻1内容", "新闻2内容"]
        
        # 调用方法
        result = self.searcher._generate_summary_via_windmill(news_content, self.stock_info)
        
        # 验证结果
        assert result == "这是生成的新闻摘要"
        
        # 验证API调用
        mock_post.assert_called_once()
    
    @patch('financial_analysis.news_search.settings')
    def test_generate_summary_via_windmill_no_config(self, mock_settings):
        """测试Windmill配置不完整时的摘要生成"""
        # 模拟配置不完整
        mock_settings.windmill_base_url = None
        mock_settings.windmill_token = None
        
        # 调用方法
        result = self.searcher._generate_summary_via_windmill([], self.stock_info)
        
        # 验证返回简单摘要
        assert "关于" in result
        assert "平安银行" in result
        assert "共找到" in result


class TestMockNewsGeneration:
    """测试模拟新闻生成"""
    
    def setup_method(self):
        """测试前准备"""
        self.searcher = NewsSearcher()
        self.stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
    
    def test_generate_mock_news(self):
        """测试生成模拟新闻"""
        result = self.searcher._generate_mock_news(self.stock_info)
        
        # 验证结果
        assert len(result) == 3  # 应该生成3条模拟新闻
        assert all(isinstance(item, NewsItem) for item in result)
        
        # 验证新闻内容包含股票名称
        for news in result:
            assert self.stock_info.name in news.title or self.stock_info.name in news.content
        
        # 验证情感分布
        sentiments = [news.sentiment for news in result]
        assert "positive" in sentiments
        assert "negative" in sentiments
        
        # 验证时间合理性
        now = datetime.now()
        for news in result:
            time_diff = now - news.publish_time
            assert time_diff.total_seconds() < 24 * 3600  # 24小时内


class TestNewsSearchIntegration:
    """测试新闻搜索模块集成"""
    
    def setup_method(self):
        """测试前准备"""
        self.searcher = NewsSearcher()
        self.stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
    
    @patch.object(NewsSearcher, '_search_via_windmill')
    def test_search_stock_news_with_cache(self, mock_search_windmill):
        """测试带缓存的新闻搜索"""
        # 模拟返回数据
        mock_news = [
            NewsItem(
                title="测试新闻",
                source="测试来源",
                publish_time=datetime.now(),
                sentiment="positive"
            )
        ]
        mock_search_windmill.return_value = mock_news
        
        # 第一次调用
        result1 = self.searcher.search_stock_news(self.stock_info, days=7)
        assert result1 == mock_news
        assert mock_search_windmill.call_count == 1
        
        # 第二次调用应该使用缓存
        result2 = self.searcher.search_stock_news(self.stock_info, days=7)
        assert result2 == mock_news
        assert mock_search_windmill.call_count == 1  # 没有增加调用次数
    
    def test_complete_news_analysis_workflow(self):
        """测试完整的新闻分析工作流"""
        # 创建测试新闻
        news_items = [
            NewsItem(
                title="平安银行业绩超预期",
                content="平安银行发布财报，业绩表现优异",
                source="财经新闻",
                publish_time=datetime.now(),
                sentiment="positive"
            ),
            NewsItem(
                title="银行业面临挑战",
                content="监管政策收紧对银行业造成压力",
                source="监管快讯",
                publish_time=datetime.now(),
                sentiment="negative"
            )
        ]
        
        # 分析情感
        sentiment_result = self.searcher.analyze_news_sentiment(news_items)
        assert sentiment_result['positive_count'] == 1
        assert sentiment_result['negative_count'] == 1
        assert sentiment_result['overall_sentiment'] == 'neutral'
        
        # 生成摘要
        with patch.object(self.searcher, '_generate_summary_via_windmill') as mock_generate:
            mock_generate.return_value = "测试摘要"
            summary = self.searcher.generate_news_summary(news_items, self.stock_info)
            assert summary == "测试摘要"
