"""
股票数据模块测试

测试股票数据获取功能，包括基本信息、价格数据和技术指标。
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import pandas as pd

from financial_analysis.stock_data import StockDataProvider
from financial_analysis.models import StockInfo, StockPrice, TechnicalIndicators


class TestStockDataProvider:
    """测试StockDataProvider类"""
    
    def setup_method(self):
        """测试前准备"""
        self.provider = StockDataProvider()
    
    def test_init(self):
        """测试初始化"""
        assert self.provider._cache == {}
        assert self.provider._cache_timeout > 0
    
    def test_cache_validation(self):
        """测试缓存验证"""
        # 测试空缓存
        assert not self.provider._is_cache_valid("non_existent_key")
        
        # 测试有效缓存
        import time
        self.provider._cache["test_key"] = {
            'data': "test_data",
            'timestamp': time.time()
        }
        assert self.provider._is_cache_valid("test_key")
        
        # 测试过期缓存
        self.provider._cache["expired_key"] = {
            'data': "test_data",
            'timestamp': time.time() - 1000  # 过期
        }
        assert not self.provider._is_cache_valid("expired_key")


class TestStockInfoRetrieval:
    """测试股票信息获取"""
    
    def setup_method(self):
        """测试前准备"""
        self.provider = StockDataProvider()
    
    @patch('financial_analysis.stock_data.ak.stock_individual_info_em')
    def test_get_china_stock_info_success(self, mock_ak_info):
        """测试成功获取中国股票信息"""
        # 模拟akshare返回数据
        mock_df = pd.DataFrame({
            'item': ['股票简称', '所属行业', '总市值'],
            'value': ['平安银行', '银行业', '150000000000']
        })
        mock_ak_info.return_value = mock_df
        
        # 调用方法
        result = self.provider._get_china_stock_info("000001")
        
        # 验证结果
        assert result is not None
        assert isinstance(result, StockInfo)
        assert result.symbol == "000001"
        assert result.name == "平安银行"
        assert result.exchange == "SZSE"  # 000001应该是深交所
        assert result.sector == "银行业"
        assert result.market_cap == 150000000000.0
    
    @patch('financial_analysis.stock_data.ak.stock_individual_info_em')
    def test_get_china_stock_info_empty_data(self, mock_ak_info):
        """测试获取中国股票信息返回空数据"""
        # 模拟akshare返回空数据
        mock_ak_info.return_value = pd.DataFrame()
        
        # 调用方法
        result = self.provider._get_china_stock_info("000001")
        
        # 验证结果
        assert result is None
    
    @patch('financial_analysis.stock_data.ak.stock_individual_info_em')
    def test_get_china_stock_info_exception(self, mock_ak_info):
        """测试获取中国股票信息异常处理"""
        # 模拟akshare抛出异常
        mock_ak_info.side_effect = Exception("Network error")
        
        # 调用方法
        result = self.provider._get_china_stock_info("000001")
        
        # 验证结果
        assert result is None
    
    @patch('financial_analysis.stock_data.yf.Ticker')
    def test_get_international_stock_info_success(self, mock_yf_ticker):
        """测试成功获取国际股票信息"""
        # 模拟yfinance返回数据
        mock_ticker = Mock()
        mock_ticker.info = {
            'longName': 'Apple Inc.',
            'currency': 'USD',
            'sector': 'Technology',
            'marketCap': 3000000000000
        }
        mock_yf_ticker.return_value = mock_ticker
        
        # 调用方法
        result = self.provider._get_international_stock_info("AAPL", "NASDAQ")
        
        # 验证结果
        assert result is not None
        assert isinstance(result, StockInfo)
        assert result.symbol == "AAPL"
        assert result.name == "Apple Inc."
        assert result.exchange == "NASDAQ"
        assert result.currency == "USD"
        assert result.sector == "Technology"
        assert result.market_cap == 3000000000000
    
    @patch('financial_analysis.stock_data.yf.Ticker')
    def test_get_international_stock_info_no_data(self, mock_yf_ticker):
        """测试获取国际股票信息无数据"""
        # 模拟yfinance返回空信息
        mock_ticker = Mock()
        mock_ticker.info = {}
        mock_yf_ticker.return_value = mock_ticker
        
        # 调用方法
        result = self.provider._get_international_stock_info("INVALID", "NASDAQ")
        
        # 验证结果
        assert result is None


class TestStockPriceRetrieval:
    """测试股票价格获取"""
    
    def setup_method(self):
        """测试前准备"""
        self.provider = StockDataProvider()
    
    @patch('financial_analysis.stock_data.ak.stock_zh_a_hist')
    def test_get_china_stock_prices_success(self, mock_ak_hist):
        """测试成功获取中国股票价格"""
        # 模拟akshare返回数据
        mock_df = pd.DataFrame({
            '日期': ['2024-01-15', '2024-01-16'],
            '开盘': [12.50, 12.60],
            '最高': [12.80, 12.90],
            '最低': [12.30, 12.40],
            '收盘': [12.65, 12.75],
            '成交量': [1000000, 1100000],
            '成交额': [12500000, 13750000]
        })
        mock_ak_hist.return_value = mock_df
        
        # 调用方法
        result = self.provider._get_china_stock_prices("000001", 7)
        
        # 验证结果
        assert len(result) == 2
        assert all(isinstance(price, StockPrice) for price in result)
        
        first_price = result[0]
        assert first_price.symbol == "000001"
        assert first_price.open_price == 12.50
        assert first_price.close_price == 12.65
        assert first_price.volume == 1000000
    
    @patch('financial_analysis.stock_data.yf.Ticker')
    def test_get_international_stock_prices_success(self, mock_yf_ticker):
        """测试成功获取国际股票价格"""
        # 模拟yfinance返回数据
        mock_ticker = Mock()
        mock_df = pd.DataFrame({
            'Open': [150.0, 151.0],
            'High': [152.0, 153.0],
            'Low': [149.0, 150.0],
            'Close': [151.0, 152.0],
            'Volume': [1000000, 1100000]
        }, index=[
            pd.Timestamp('2024-01-15'),
            pd.Timestamp('2024-01-16')
        ])
        mock_ticker.history.return_value = mock_df
        mock_yf_ticker.return_value = mock_ticker
        
        # 调用方法
        result = self.provider._get_international_stock_prices("AAPL", 7, "NASDAQ")
        
        # 验证结果
        assert len(result) == 2
        assert all(isinstance(price, StockPrice) for price in result)
        
        first_price = result[0]
        assert first_price.symbol == "AAPL"
        assert first_price.open_price == 150.0
        assert first_price.close_price == 151.0
        assert first_price.volume == 1000000


class TestTechnicalIndicators:
    """测试技术指标计算"""
    
    def setup_method(self):
        """测试前准备"""
        self.provider = StockDataProvider()
    
    def test_calculate_rsi(self):
        """测试RSI计算"""
        # 创建测试价格序列
        prices = pd.Series([10, 11, 10.5, 12, 11.5, 13, 12.5, 14, 13.5, 15, 14.5, 16, 15.5, 17, 16.5])
        
        # 计算RSI
        rsi = self.provider._calculate_rsi(prices, period=14)
        
        # 验证结果
        assert isinstance(rsi, pd.Series)
        assert len(rsi) == len(prices)
        # RSI应该在0-100之间
        valid_rsi = rsi.dropna()
        assert all(0 <= val <= 100 for val in valid_rsi)
    
    def test_calculate_macd(self):
        """测试MACD计算"""
        # 创建测试价格序列
        prices = pd.Series(range(10, 30))  # 递增序列
        
        # 计算MACD
        macd = self.provider._calculate_macd(prices)
        
        # 验证结果
        assert isinstance(macd, pd.Series)
        assert len(macd) == len(prices)
    
    def test_calculate_kdj(self):
        """测试KDJ计算"""
        # 创建测试数据
        df = pd.DataFrame({
            'high': [12, 13, 14, 13, 15, 14, 16, 15, 17],
            'low': [10, 11, 12, 11, 13, 12, 14, 13, 15],
            'close': [11, 12, 13, 12, 14, 13, 15, 14, 16]
        })
        
        # 计算KDJ
        k, d, j = self.provider._calculate_kdj(df)
        
        # 验证结果
        assert isinstance(k, pd.Series)
        assert isinstance(d, pd.Series)
        assert isinstance(j, pd.Series)
        assert len(k) == len(df)
        assert len(d) == len(df)
        assert len(j) == len(df)
    
    @patch.object(StockDataProvider, 'get_stock_prices')
    def test_calculate_technical_indicators_success(self, mock_get_prices):
        """测试成功计算技术指标"""
        # 模拟价格数据
        mock_prices = []
        for i in range(30):
            price = StockPrice(
                symbol="000001",
                date=datetime.now() - timedelta(days=29-i),
                open_price=10.0 + i * 0.1,
                high_price=10.5 + i * 0.1,
                low_price=9.5 + i * 0.1,
                close_price=10.0 + i * 0.1,
                volume=1000000
            )
            mock_prices.append(price)
        
        mock_get_prices.return_value = mock_prices
        
        # 调用方法
        result = self.provider._calculate_technical_indicators(
            pd.DataFrame(), "000001"
        )
        
        # 由于我们模拟了get_stock_prices，实际会调用真实的计算逻辑
        # 这里主要测试方法不会抛出异常
        # 在实际实现中，需要更详细的测试
    
    def test_calculate_technical_indicators_insufficient_data(self):
        """测试数据不足时的技术指标计算"""
        # 创建少量数据
        df = pd.DataFrame({
            'date': [datetime.now()],
            'close': [10.0]
        })
        
        # 调用方法
        result = self.provider._calculate_technical_indicators(df, "000001")
        
        # 验证结果
        assert result is None


class TestStockDataIntegration:
    """测试股票数据模块集成"""
    
    def setup_method(self):
        """测试前准备"""
        self.provider = StockDataProvider()
    
    @patch.object(StockDataProvider, '_get_china_stock_info')
    def test_get_stock_info_china_stock(self, mock_get_china_info):
        """测试获取中国股票信息的完整流程"""
        # 模拟返回数据
        expected_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
        mock_get_china_info.return_value = expected_info
        
        # 调用方法
        result = self.provider.get_stock_info("000001", "SZSE")
        
        # 验证结果
        assert result == expected_info
        mock_get_china_info.assert_called_once_with("000001")
    
    @patch.object(StockDataProvider, '_get_international_stock_info')
    def test_get_stock_info_international_stock(self, mock_get_intl_info):
        """测试获取国际股票信息的完整流程"""
        # 模拟返回数据
        expected_info = StockInfo(
            symbol="AAPL",
            name="Apple Inc.",
            exchange="NASDAQ"
        )
        mock_get_intl_info.return_value = expected_info
        
        # 调用方法
        result = self.provider.get_stock_info("AAPL", "NASDAQ")
        
        # 验证结果
        assert result == expected_info
        mock_get_intl_info.assert_called_once_with("AAPL", "NASDAQ")
    
    def test_get_stock_info_with_cache(self):
        """测试缓存机制"""
        # 第一次调用
        with patch.object(self.provider, '_get_china_stock_info') as mock_get_info:
            mock_info = StockInfo(symbol="000001", name="平安银行", exchange="SZSE")
            mock_get_info.return_value = mock_info
            
            result1 = self.provider.get_stock_info("000001", "SZSE")
            assert result1 == mock_info
            assert mock_get_info.call_count == 1
            
            # 第二次调用应该使用缓存
            result2 = self.provider.get_stock_info("000001", "SZSE")
            assert result2 == mock_info
            assert mock_get_info.call_count == 1  # 没有增加调用次数
