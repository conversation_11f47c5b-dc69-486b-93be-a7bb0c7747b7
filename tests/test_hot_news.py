"""
热点信息功能单元测试
"""

import pytest
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

from financial_analysis.models import HotNewsItem, HotNewsChannel, HotNewsCache
from financial_analysis.hot_news_collector import HotNewsCollector
from financial_analysis.hot_news_analyzer import HotNewsAnalyzer
from financial_analysis.hot_news_cache import HotNewsCacheManager
from financial_analysis.hot_news_pusher import HotNewsPusher
from financial_analysis.hot_news_manager import HotNewsManager


class TestHotNewsModels:
    """测试热点信息数据模型"""
    
    def test_hot_news_item_creation(self):
        """测试热点信息条目创建"""
        news_item = HotNewsItem(
            news_id="test_123",
            title="测试新闻标题",
            content="测试新闻内容",
            source="测试来源",
            channel_id="test_channel",
            url="https://example.com/news/123",
            publish_time=datetime.now(),
            fetch_time=datetime.now(),
            heat_score=85.5,
            category="财经",
            tags=["股票", "市场"],
            keywords=["测试", "新闻"],
            sentiment="positive",
            importance_level="high"
        )
        
        assert news_item.news_id == "test_123"
        assert news_item.title == "测试新闻标题"
        assert news_item.heat_score == 85.5
        assert news_item.sentiment == "positive"
        assert news_item.importance_level == "high"
        assert "股票" in news_item.tags
        assert "测试" in news_item.keywords
    
    def test_hot_news_channel_creation(self):
        """测试热点信息渠道创建"""
        channel = HotNewsChannel(
            channel_id="sina_rss",
            name="新浪财经RSS",
            channel_type="rss",
            url="https://feed.sina.com.cn/finance",
            enabled=True,
            priority=1,
            fetch_interval=300
        )
        
        assert channel.channel_id == "sina_rss"
        assert channel.name == "新浪财经RSS"
        assert channel.channel_type == "rss"
        assert channel.enabled is True
        assert channel.priority == 1
    
    def test_hot_news_cache_creation(self):
        """测试热点信息缓存创建"""
        cache_item = HotNewsCache(
            cache_key="test_cache",
            data={"test": "data"},
            created_time=datetime.now(),
            expire_time=datetime.now() + timedelta(hours=1),
            access_count=5
        )
        
        assert cache_item.cache_key == "test_cache"
        assert cache_item.data == {"test": "data"}
        assert cache_item.access_count == 5


class TestHotNewsCollector:
    """测试热点信息收集器"""
    
    @pytest.fixture
    def collector(self):
        """创建收集器实例"""
        return HotNewsCollector()
    
    def test_collector_initialization(self, collector):
        """测试收集器初始化"""
        assert collector is not None
        assert hasattr(collector, '_channels')
        assert hasattr(collector, '_session')
    
    @patch('financial_analysis.hot_news_collector.feedparser.parse')
    def test_fetch_from_rss(self, mock_parse, collector):
        """测试RSS获取"""
        # 模拟RSS响应
        mock_entry = Mock()
        mock_entry.title = "测试RSS新闻"
        mock_entry.description = "测试RSS内容"
        mock_entry.link = "https://example.com/rss/news"
        mock_entry.published = "Mon, 01 Jan 2024 12:00:00 GMT"
        
        mock_feed = Mock()
        mock_feed.entries = [mock_entry]
        mock_parse.return_value = mock_feed
        
        channel = HotNewsChannel(
            channel_id="test_rss",
            name="测试RSS",
            channel_type="rss",
            url="https://example.com/rss"
        )
        
        news_items = collector._fetch_from_rss(channel)
        
        assert len(news_items) == 1
        assert news_items[0].title == "测试RSS新闻"
        assert news_items[0].source == "测试RSS"
        assert news_items[0].channel_id == "test_rss"
    
    def test_generate_news_id(self, collector):
        """测试新闻ID生成"""
        title = "测试新闻标题"
        url = "https://example.com/news/123"
        
        news_id = collector._generate_news_id(title, url)
        
        assert news_id is not None
        assert len(news_id) == 32  # MD5哈希长度
        
        # 相同输入应该生成相同ID
        news_id2 = collector._generate_news_id(title, url)
        assert news_id == news_id2
    
    def test_deduplicate_news(self, collector):
        """测试新闻去重"""
        news1 = HotNewsItem(
            news_id="test_1",
            title="新闻1",
            source="来源1",
            channel_id="channel_1",
            publish_time=datetime.now(),
            fetch_time=datetime.now()
        )
        
        news2 = HotNewsItem(
            news_id="test_1",  # 相同ID
            title="新闻1重复",
            source="来源2",
            channel_id="channel_2",
            publish_time=datetime.now(),
            fetch_time=datetime.now()
        )
        
        news3 = HotNewsItem(
            news_id="test_2",
            title="新闻2",
            source="来源1",
            channel_id="channel_1",
            publish_time=datetime.now(),
            fetch_time=datetime.now()
        )
        
        news_list = [news1, news2, news3]
        unique_news = collector._deduplicate_news(news_list)
        
        assert len(unique_news) == 2
        assert unique_news[0].news_id == "test_1"
        assert unique_news[1].news_id == "test_2"


class TestHotNewsAnalyzer:
    """测试热点信息分析器"""
    
    @pytest.fixture
    def analyzer(self):
        """创建分析器实例"""
        return HotNewsAnalyzer()
    
    def test_analyzer_initialization(self, analyzer):
        """测试分析器初始化"""
        assert analyzer is not None
        assert hasattr(analyzer, '_cache')
        assert hasattr(analyzer, '_cache_timeout')
    
    def test_simple_analysis(self, analyzer):
        """测试简单分析"""
        news_item = HotNewsItem(
            news_id="test_123",
            title="测试新闻标题",
            content="测试新闻内容",
            source="测试来源",
            channel_id="test_channel",
            publish_time=datetime.now() - timedelta(hours=1),
            fetch_time=datetime.now()
        )
        
        result = analyzer._simple_analysis(news_item)
        
        assert result is not None
        assert "is_historical" in result
        assert "sentiment" in result
        assert "importance_level" in result
        assert "keywords" in result
        assert result["sentiment"] in ["positive", "negative", "neutral"]
        assert result["importance_level"] in ["high", "medium", "low"]
    
    def test_filter_historical_news(self, analyzer):
        """测试过滤历史信息"""
        news1 = HotNewsItem(
            news_id="test_1",
            title="新闻1",
            source="来源1",
            channel_id="channel_1",
            publish_time=datetime.now(),
            fetch_time=datetime.now(),
            is_historical=False
        )
        
        news2 = HotNewsItem(
            news_id="test_2",
            title="历史新闻",
            source="来源2",
            channel_id="channel_2",
            publish_time=datetime.now(),
            fetch_time=datetime.now(),
            is_historical=True
        )
        
        news_list = [news1, news2]
        filtered_news = analyzer.filter_historical_news(news_list)
        
        assert len(filtered_news) == 1
        assert filtered_news[0].news_id == "test_1"
        assert not filtered_news[0].is_historical
    
    def test_filter_by_importance(self, analyzer):
        """测试按重要程度过滤"""
        news1 = HotNewsItem(
            news_id="test_1",
            title="高重要性新闻",
            source="来源1",
            channel_id="channel_1",
            publish_time=datetime.now(),
            fetch_time=datetime.now(),
            importance_level="high"
        )
        
        news2 = HotNewsItem(
            news_id="test_2",
            title="低重要性新闻",
            source="来源2",
            channel_id="channel_2",
            publish_time=datetime.now(),
            fetch_time=datetime.now(),
            importance_level="low"
        )
        
        news_list = [news1, news2]
        filtered_news = analyzer.filter_by_importance(news_list, "medium")
        
        assert len(filtered_news) == 1
        assert filtered_news[0].news_id == "test_1"
        assert filtered_news[0].importance_level == "high"


class TestHotNewsCacheManager:
    """测试热点信息缓存管理器"""
    
    @pytest.fixture
    def cache_manager(self, tmp_path):
        """创建缓存管理器实例"""
        return HotNewsCacheManager(cache_dir=str(tmp_path))
    
    def test_cache_manager_initialization(self, cache_manager):
        """测试缓存管理器初始化"""
        assert cache_manager is not None
        assert hasattr(cache_manager, '_memory_cache')
        assert hasattr(cache_manager, '_cache_dir')
    
    def test_set_and_get_cache(self, cache_manager):
        """测试设置和获取缓存"""
        test_data = {"test": "data", "number": 123}
        
        # 设置缓存
        result = cache_manager.set("test_key", test_data, 60)
        assert result is True
        
        # 获取缓存
        cached_data = cache_manager.get("test_key")
        assert cached_data == test_data
    
    def test_cache_expiration(self, cache_manager):
        """测试缓存过期"""
        test_data = {"test": "data"}
        
        # 设置短期缓存
        cache_manager.set("expire_key", test_data, 1)
        
        # 立即获取应该成功
        cached_data = cache_manager.get("expire_key")
        assert cached_data == test_data
        
        # 模拟过期
        import time
        time.sleep(1.1)
        
        # 过期后获取应该返回None
        expired_data = cache_manager.get("expire_key")
        assert expired_data is None
    
    def test_cache_news_list(self, cache_manager):
        """测试缓存新闻列表"""
        news_items = [
            HotNewsItem(
                news_id="test_1",
                title="新闻1",
                source="来源1",
                channel_id="channel_1",
                publish_time=datetime.now(),
                fetch_time=datetime.now()
            ),
            HotNewsItem(
                news_id="test_2",
                title="新闻2",
                source="来源2",
                channel_id="channel_2",
                publish_time=datetime.now(),
                fetch_time=datetime.now()
            )
        ]
        
        # 缓存新闻列表
        result = cache_manager.cache_news_list(news_items, "test_news")
        assert result is True
        
        # 获取缓存的新闻列表
        cached_news = cache_manager.get_cached_news_list("test_news")
        assert len(cached_news) == 2
        assert cached_news[0].news_id == "test_1"
        assert cached_news[1].news_id == "test_2"


class TestHotNewsPusher:
    """测试热点信息推送器"""
    
    @pytest.fixture
    def pusher(self):
        """创建推送器实例"""
        return HotNewsPusher()
    
    def test_pusher_initialization(self, pusher):
        """测试推送器初始化"""
        assert pusher is not None
        assert hasattr(pusher, '_push_history')
        assert hasattr(pusher, '_last_push_time')
    
    def test_build_push_message(self, pusher):
        """测试构造推送消息"""
        news_item = HotNewsItem(
            news_id="test_123",
            title="测试新闻标题",
            content="测试新闻内容",
            source="测试来源",
            channel_id="test_channel",
            publish_time=datetime.now(),
            fetch_time=datetime.now(),
            importance_level="high",
            summary="测试摘要",
            keywords=["测试", "新闻", "推送"]
        )
        
        message = pusher._build_push_message(news_item)
        
        assert "测试新闻标题" in message
        assert "测试来源" in message
        assert "测试摘要" in message
        assert "#测试" in message
        assert "🔥" in message  # 高重要性的emoji
    
    def test_filter_pending_news(self, pusher):
        """测试过滤待推送新闻"""
        news1 = HotNewsItem(
            news_id="test_1",
            title="待推送新闻",
            source="来源1",
            channel_id="channel_1",
            publish_time=datetime.now(),
            fetch_time=datetime.now(),
            is_pushed=False,
            is_historical=False,
            importance_level="high"
        )
        
        news2 = HotNewsItem(
            news_id="test_2",
            title="已推送新闻",
            source="来源2",
            channel_id="channel_2",
            publish_time=datetime.now(),
            fetch_time=datetime.now(),
            is_pushed=True,
            is_historical=False,
            importance_level="high"
        )
        
        news3 = HotNewsItem(
            news_id="test_3",
            title="历史新闻",
            source="来源3",
            channel_id="channel_3",
            publish_time=datetime.now(),
            fetch_time=datetime.now(),
            is_pushed=False,
            is_historical=True,
            importance_level="high"
        )
        
        news_list = [news1, news2, news3]
        pending_news = pusher._filter_pending_news(news_list)
        
        assert len(pending_news) == 1
        assert pending_news[0].news_id == "test_1"


class TestHotNewsManager:
    """测试热点信息管理器"""
    
    @pytest.fixture
    def manager(self):
        """创建管理器实例"""
        return HotNewsManager()
    
    def test_manager_initialization(self, manager):
        """测试管理器初始化"""
        assert manager is not None
        assert hasattr(manager, 'collector')
        assert hasattr(manager, 'analyzer')
        assert hasattr(manager, 'pusher')
        assert hasattr(manager, '_news_storage')
    
    def test_get_news_by_id(self, manager):
        """测试根据ID获取新闻"""
        news_item = HotNewsItem(
            news_id="test_123",
            title="测试新闻",
            source="测试来源",
            channel_id="test_channel",
            publish_time=datetime.now(),
            fetch_time=datetime.now()
        )
        
        # 存储新闻
        manager._news_storage["test_123"] = news_item
        
        # 获取新闻
        retrieved_news = manager.get_news_by_id("test_123")
        
        assert retrieved_news is not None
        assert retrieved_news.news_id == "test_123"
        assert retrieved_news.title == "测试新闻"
        
        # 获取不存在的新闻
        non_existent = manager.get_news_by_id("non_existent")
        assert non_existent is None
    
    def test_calculate_relevance_score(self, manager):
        """测试计算相关度评分"""
        news_item = HotNewsItem(
            news_id="test_123",
            title="股票市场分析报告",
            content="详细的股票市场分析内容",
            source="财经日报",
            channel_id="finance_channel",
            publish_time=datetime.now(),
            fetch_time=datetime.now(),
            keywords=["股票", "市场", "分析"],
            importance_level="high"
        )
        
        # 测试标题匹配
        score1 = manager._calculate_relevance_score(news_item, "股票")
        assert score1 > 0
        
        # 测试关键词匹配
        score2 = manager._calculate_relevance_score(news_item, "市场")
        assert score2 > 0
        
        # 测试无匹配
        score3 = manager._calculate_relevance_score(news_item, "无关词汇")
        assert score3 == 0
