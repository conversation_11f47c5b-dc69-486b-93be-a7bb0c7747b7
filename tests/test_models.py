"""
数据模型测试模块

测试所有数据模型的创建、验证和序列化功能。
"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from financial_analysis.models import (
    StockInfo, StockPrice, NewsItem, TechnicalIndicators, AnalysisReport
)


class TestStockInfo:
    """测试StockInfo模型"""
    
    def test_create_stock_info(self):
        """测试创建股票信息"""
        stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE",
            currency="CNY",
            sector="银行业",
            market_cap=150000000000
        )
        
        assert stock_info.symbol == "000001"
        assert stock_info.name == "平安银行"
        assert stock_info.exchange == "SZSE"
        assert stock_info.currency == "CNY"
        assert stock_info.sector == "银行业"
        assert stock_info.market_cap == 150000000000
    
    def test_stock_info_required_fields(self):
        """测试必填字段验证"""
        with pytest.raises(ValidationError):
            StockInfo()  # 缺少必填字段
        
        with pytest.raises(ValidationError):
            StockInfo(symbol="000001")  # 缺少name和exchange
    
    def test_stock_info_defaults(self):
        """测试默认值"""
        stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
        
        assert stock_info.currency == "CNY"  # 默认值
        assert stock_info.sector is None
        assert stock_info.market_cap is None
    
    def test_stock_info_json_serialization(self):
        """测试JSON序列化"""
        stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
        
        json_data = stock_info.model_dump_json()
        assert "000001" in json_data
        assert "平安银行" in json_data
        
        # 测试反序列化
        restored = StockInfo.model_validate_json(json_data)
        assert restored.symbol == stock_info.symbol
        assert restored.name == stock_info.name


class TestStockPrice:
    """测试StockPrice模型"""
    
    def test_create_stock_price(self):
        """测试创建股票价格"""
        price = StockPrice(
            symbol="000001",
            date=datetime(2024, 1, 15),
            open_price=12.50,
            high_price=12.80,
            low_price=12.30,
            close_price=12.65,
            volume=1000000,
            turnover=12500000.0
        )
        
        assert price.symbol == "000001"
        assert price.date == datetime(2024, 1, 15)
        assert price.open_price == 12.50
        assert price.high_price == 12.80
        assert price.low_price == 12.30
        assert price.close_price == 12.65
        assert price.volume == 1000000
        assert price.turnover == 12500000.0
    
    def test_stock_price_type_conversion(self):
        """测试类型自动转换"""
        price = StockPrice(
            symbol="000001",
            date="2024-01-15",  # 字符串转datetime
            open_price="12.50",  # 字符串转float
            high_price="12.80",
            low_price="12.30",
            close_price="12.65",
            volume="1000000",  # 字符串转int
        )
        
        assert isinstance(price.date, datetime)
        assert isinstance(price.open_price, float)
        assert isinstance(price.volume, int)
    
    def test_stock_price_validation(self):
        """测试数据验证"""
        with pytest.raises(ValidationError):
            StockPrice(
                symbol="",  # 空字符串
                date="invalid-date",  # 无效日期
                open_price="not-a-number",  # 无法转换的数字
                high_price=12.80,
                low_price=12.30,
                close_price=12.65,
                volume=1000000
            )


class TestNewsItem:
    """测试NewsItem模型"""
    
    def test_create_news_item(self):
        """测试创建新闻条目"""
        news = NewsItem(
            title="平安银行发布三季度财报",
            content="平安银行今日发布三季度财报，业绩超预期...",
            source="财经新闻网",
            publish_time=datetime.now(),
            url="https://example.com/news",
            sentiment="positive"
        )
        
        assert news.title == "平安银行发布三季度财报"
        assert news.source == "财经新闻网"
        assert news.sentiment == "positive"
    
    def test_news_item_optional_fields(self):
        """测试可选字段"""
        news = NewsItem(
            title="测试新闻",
            source="测试来源",
            publish_time=datetime.now()
        )
        
        assert news.content is None
        assert news.url is None
        assert news.sentiment is None


class TestTechnicalIndicators:
    """测试TechnicalIndicators模型"""
    
    def test_create_technical_indicators(self):
        """测试创建技术指标"""
        indicators = TechnicalIndicators(
            symbol="000001",
            date=datetime.now(),
            ma5=12.50,
            ma20=12.30,
            rsi=65.5,
            macd=0.15,
            kdj_k=75.2,
            kdj_d=68.9,
            kdj_j=87.8
        )
        
        assert indicators.symbol == "000001"
        assert indicators.ma5 == 12.50
        assert indicators.rsi == 65.5
        assert indicators.macd == 0.15
    
    def test_technical_indicators_optional_fields(self):
        """测试可选技术指标字段"""
        indicators = TechnicalIndicators(
            symbol="000001",
            date=datetime.now()
        )
        
        assert indicators.ma5 is None
        assert indicators.rsi is None
        assert indicators.macd is None


class TestAnalysisReport:
    """测试AnalysisReport模型"""
    
    def test_create_analysis_report(self):
        """测试创建分析报告"""
        stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
        
        indicators = TechnicalIndicators(
            symbol="000001",
            date=datetime.now(),
            ma5=12.50,
            rsi=65.5
        )
        
        report = AnalysisReport(
            symbol="000001",
            stock_info=stock_info,
            analysis_date=datetime.now(),
            current_price=12.65,
            price_change=0.15,
            price_change_percent=1.2,
            technical_indicators=indicators,
            trend_analysis="短期上升趋势",
            support_resistance={"support": 12.0, "resistance": 13.0},
            fundamental_analysis="基本面良好",
            news_sentiment="positive",
            news_summary="整体新闻偏向正面",
            overall_rating="买入",
            risk_level="中",
            investment_advice="建议适量买入",
            ai_analysis="AI分析结果"
        )
        
        assert report.symbol == "000001"
        assert report.stock_info.name == "平安银行"
        assert report.current_price == 12.65
        assert report.overall_rating == "买入"
    
    def test_analysis_report_json_serialization(self):
        """测试分析报告JSON序列化"""
        stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
        
        indicators = TechnicalIndicators(
            symbol="000001",
            date=datetime.now()
        )
        
        report = AnalysisReport(
            symbol="000001",
            stock_info=stock_info,
            analysis_date=datetime.now(),
            current_price=12.65,
            price_change=0.15,
            price_change_percent=1.2,
            technical_indicators=indicators,
            trend_analysis="测试趋势",
            support_resistance={},
            fundamental_analysis="测试基本面",
            news_sentiment="neutral",
            news_summary="测试摘要",
            overall_rating="持有",
            risk_level="低",
            investment_advice="测试建议",
            ai_analysis="测试AI分析"
        )
        
        json_data = report.model_dump_json()
        assert "000001" in json_data
        assert "平安银行" in json_data
        
        # 验证可以反序列化
        restored = AnalysisReport.model_validate_json(json_data)
        assert restored.symbol == report.symbol
        assert restored.stock_info.name == report.stock_info.name
