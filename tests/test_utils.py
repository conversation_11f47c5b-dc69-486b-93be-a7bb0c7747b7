"""
工具函数测试模块

测试utils模块中的各种工具函数。
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from financial_analysis.utils import (
    normalize_stock_symbol,
    get_exchange_suffix,
    format_currency,
    calculate_date_range,
    safe_float,
    safe_int,
    setup_logging
)


class TestStockSymbolUtils:
    """测试股票代码相关工具函数"""
    
    def test_normalize_stock_symbol(self):
        """测试股票代码标准化"""
        # 测试中国A股代码
        assert normalize_stock_symbol("000001") == "000001"
        assert normalize_stock_symbol("SH.600036") == "600036"
        assert normalize_stock_symbol("SZ.000002") == "000002"
        assert normalize_stock_symbol("000001.SZ") == "000001"
        
        # 测试美股代码
        assert normalize_stock_symbol("AAPL") == "AAPL"
        assert normalize_stock_symbol("aapl") == "AAPL"
        
        # 测试带空格的代码
        assert normalize_stock_symbol("  000001  ") == "000001"
    
    def test_normalize_stock_symbol_with_exchange(self):
        """测试指定交易所的股票代码标准化"""
        assert normalize_stock_symbol("000001", "SZSE") == "000001"
        assert normalize_stock_symbol("600036", "SSE") == "600036"
        assert normalize_stock_symbol("AAPL", "NASDAQ") == "AAPL"
    
    def test_get_exchange_suffix(self):
        """测试获取交易所后缀"""
        assert get_exchange_suffix("SSE") == ".SS"
        assert get_exchange_suffix("SZSE") == ".SZ"
        assert get_exchange_suffix("HK") == ".HK"
        assert get_exchange_suffix("NASDAQ") == ""
        assert get_exchange_suffix("NYSE") == ""
        assert get_exchange_suffix("UNKNOWN") == ""
    
    def test_get_exchange_suffix_case_insensitive(self):
        """测试交易所后缀大小写不敏感"""
        assert get_exchange_suffix("sse") == ".SS"
        assert get_exchange_suffix("szse") == ".SZ"
        assert get_exchange_suffix("hk") == ".HK"


class TestCurrencyUtils:
    """测试货币格式化工具函数"""
    
    def test_format_currency_cny(self):
        """测试人民币格式化"""
        assert format_currency(1000, "CNY") == "¥1000.00"
        assert format_currency(15000, "CNY") == "¥1.50万"
        assert format_currency(150000000, "CNY") == "¥1.50亿"
        assert format_currency(1500000000, "CNY") == "¥15.00亿"
    
    def test_format_currency_usd(self):
        """测试美元格式化"""
        assert format_currency(1000, "USD") == "$1000.00"
        assert format_currency(1500000, "USD") == "$1.50M"
        assert format_currency(1500000000, "USD") == "$1.50B"
        assert format_currency(15000000000, "USD") == "$15.00B"
    
    def test_format_currency_other(self):
        """测试其他货币格式化"""
        assert format_currency(1000, "EUR") == "1000.00 EUR"
        assert format_currency(1500.5, "JPY") == "1500.50 JPY"
    
    def test_format_currency_edge_cases(self):
        """测试边界情况"""
        assert format_currency(0, "CNY") == "¥0.00"
        assert format_currency(-1000, "CNY") == "¥-1000.00"
        assert format_currency(9999, "CNY") == "¥9999.00"  # 刚好不到万
        assert format_currency(10000, "CNY") == "¥1.00万"  # 刚好1万


class TestDateUtils:
    """测试日期相关工具函数"""
    
    def test_calculate_date_range(self):
        """测试日期范围计算"""
        start_date, end_date = calculate_date_range(7)
        
        # 检查日期类型
        assert isinstance(start_date, datetime)
        assert isinstance(end_date, datetime)
        
        # 检查日期范围
        assert end_date > start_date
        delta = end_date - start_date
        assert delta.days == 7
    
    def test_calculate_date_range_different_days(self):
        """测试不同天数的日期范围"""
        start_date_30, end_date_30 = calculate_date_range(30)
        start_date_60, end_date_60 = calculate_date_range(60)
        
        # 30天范围应该比60天范围的开始日期更晚
        assert start_date_30 > start_date_60
        
        # 结束日期应该相近（都是当前时间）
        time_diff = abs((end_date_30 - end_date_60).total_seconds())
        assert time_diff < 1  # 小于1秒差异


class TestSafeConversion:
    """测试安全类型转换函数"""
    
    def test_safe_float(self):
        """测试安全浮点数转换"""
        assert safe_float(12.5) == 12.5
        assert safe_float("12.5") == 12.5
        assert safe_float("12") == 12.0
        assert safe_float(None) == 0.0
        assert safe_float("not-a-number") == 0.0
        assert safe_float("") == 0.0
        assert safe_float([]) == 0.0
    
    def test_safe_float_with_default(self):
        """测试带默认值的安全浮点数转换"""
        assert safe_float(None, -1.0) == -1.0
        assert safe_float("invalid", 99.9) == 99.9
        assert safe_float("12.5", 99.9) == 12.5
    
    def test_safe_int(self):
        """测试安全整数转换"""
        assert safe_int(12) == 12
        assert safe_int("12") == 12
        assert safe_int(12.7) == 12  # 截断小数部分
        assert safe_int(None) == 0
        assert safe_int("not-a-number") == 0
        assert safe_int("") == 0
        assert safe_int([]) == 0
    
    def test_safe_int_with_default(self):
        """测试带默认值的安全整数转换"""
        assert safe_int(None, -1) == -1
        assert safe_int("invalid", 999) == 999
        assert safe_int("12", 999) == 12
        assert safe_int(12.7, 999) == 12


class TestLoggingSetup:
    """测试日志配置"""
    
    @patch('financial_analysis.utils.logger')
    @patch('os.makedirs')
    @patch('os.path.exists')
    @patch('os.path.dirname')
    def test_setup_logging(self, mock_dirname, mock_exists, mock_makedirs, mock_logger):
        """测试日志系统配置"""
        # 模拟日志目录不存在
        mock_dirname.return_value = "logs"
        mock_exists.return_value = False
        
        # 模拟logger对象
        mock_logger.remove = MagicMock()
        mock_logger.add = MagicMock()
        
        # 调用函数
        setup_logging()
        
        # 验证调用
        mock_makedirs.assert_called_once_with("logs")
        mock_logger.remove.assert_called_once()
        assert mock_logger.add.call_count == 2  # 控制台和文件两个处理器
    
    @patch('financial_analysis.utils.logger')
    @patch('os.path.exists')
    @patch('os.path.dirname')
    def test_setup_logging_dir_exists(self, mock_dirname, mock_exists, mock_logger):
        """测试日志目录已存在的情况"""
        # 模拟日志目录已存在
        mock_dirname.return_value = "logs"
        mock_exists.return_value = True
        
        # 模拟logger对象
        mock_logger.remove = MagicMock()
        mock_logger.add = MagicMock()
        
        # 调用函数
        setup_logging()
        
        # 验证logger配置被调用
        mock_logger.remove.assert_called_once()
        assert mock_logger.add.call_count == 2


class TestUtilsIntegration:
    """测试工具函数集成"""
    
    def test_stock_symbol_processing_pipeline(self):
        """测试股票代码处理流水线"""
        # 模拟完整的股票代码处理流程
        raw_symbol = "  SH.600036  "
        
        # 标准化
        normalized = normalize_stock_symbol(raw_symbol)
        assert normalized == "600036"
        
        # 获取交易所后缀
        suffix = get_exchange_suffix("SSE")
        assert suffix == ".SS"
        
        # 组合完整符号
        full_symbol = normalized + suffix
        assert full_symbol == "600036.SS"
    
    def test_data_processing_pipeline(self):
        """测试数据处理流水线"""
        # 模拟数据处理流程
        raw_price = "12.50"
        raw_volume = "1000000"
        raw_market_cap = "150000000000"
        
        # 安全转换
        price = safe_float(raw_price)
        volume = safe_int(raw_volume)
        market_cap = safe_float(raw_market_cap)
        
        assert price == 12.5
        assert volume == 1000000
        assert market_cap == 150000000000.0
        
        # 格式化显示
        formatted_price = f"{price:.2f}"
        formatted_market_cap = format_currency(market_cap, "CNY")
        
        assert formatted_price == "12.50"
        assert formatted_market_cap == "¥1500.00亿"
