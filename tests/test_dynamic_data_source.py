"""
动态数据源管理功能测试

测试动态数据源管理的各项功能，包括：
1. 数据源注册器测试
2. 适配器测试
3. API接口测试
4. 数据收集测试
"""

import unittest
import tempfile
import os
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

from financial_analysis.models import DataSourceConfig, UnifiedNewsData
from financial_analysis.data_source_registry import DataSourceRegistry
from financial_analysis.data_source_adapters import RSSAdapter, APIAdapter
from financial_analysis.data_source_api import DataSourceAPI
from financial_analysis.hot_news_collector import HotNewsCollector


class TestDataSourceRegistry(unittest.TestCase):
    """数据源注册器测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json')
        self.temp_file.write('[]')
        self.temp_file.close()
        
        # 创建注册器实例
        self.registry = DataSourceRegistry(config_file=self.temp_file.name)
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_register_source(self):
        """测试注册数据源"""
        config = DataSourceConfig(
            source_id="test_rss",
            name="测试RSS",
            source_type="rss",
            adapter_class="RSSAdapter",
            config={"url": "https://example.com/rss.xml"}
        )
        
        # 注册数据源
        success = self.registry.register_source(config)
        self.assertTrue(success)
        
        # 验证数据源已注册
        registered_config = self.registry.get_source("test_rss")
        self.assertIsNotNone(registered_config)
        self.assertEqual(registered_config.name, "测试RSS")
    
    def test_unregister_source(self):
        """测试注销数据源"""
        # 先注册一个数据源
        config = DataSourceConfig(
            source_id="test_api",
            name="测试API",
            source_type="api",
            adapter_class="APIAdapter",
            config={"url": "https://api.example.com/news"}
        )
        self.registry.register_source(config)
        
        # 注销数据源
        success = self.registry.unregister_source("test_api")
        self.assertTrue(success)
        
        # 验证数据源已注销
        registered_config = self.registry.get_source("test_api")
        self.assertIsNone(registered_config)
    
    def test_update_source(self):
        """测试更新数据源"""
        # 先注册一个数据源
        config = DataSourceConfig(
            source_id="test_update",
            name="测试更新",
            source_type="rss",
            adapter_class="RSSAdapter",
            config={"url": "https://example.com/rss.xml"},
            fetch_interval=300
        )
        self.registry.register_source(config)
        
        # 更新数据源
        success = self.registry.update_source("test_update", {
            "fetch_interval": 600,
            "enabled": False
        })
        self.assertTrue(success)
        
        # 验证更新结果
        updated_config = self.registry.get_source("test_update")
        self.assertEqual(updated_config.fetch_interval, 600)
        self.assertFalse(updated_config.enabled)
    
    def test_list_sources(self):
        """测试列出数据源"""
        # 注册多个数据源
        configs = [
            DataSourceConfig(
                source_id="test1",
                name="测试1",
                source_type="rss",
                adapter_class="RSSAdapter",
                config={"url": "https://example1.com/rss.xml"},
                enabled=True,
                priority=1
            ),
            DataSourceConfig(
                source_id="test2",
                name="测试2",
                source_type="api",
                adapter_class="APIAdapter",
                config={"url": "https://api.example2.com/news"},
                enabled=False,
                priority=2
            )
        ]
        
        for config in configs:
            self.registry.register_source(config)
        
        # 测试列出所有数据源
        all_sources = self.registry.list_sources()
        self.assertGreaterEqual(len(all_sources), 2)
        
        # 测试只列出启用的数据源
        enabled_sources = self.registry.list_sources(enabled_only=True)
        enabled_ids = [s.source_id for s in enabled_sources]
        self.assertIn("test1", enabled_ids)
        self.assertNotIn("test2", enabled_ids)


class TestDataSourceAdapters(unittest.TestCase):
    """数据源适配器测试"""
    
    def test_rss_adapter_validation(self):
        """测试RSS适配器配置验证"""
        # 有效配置
        valid_config = DataSourceConfig(
            source_id="test_rss",
            name="测试RSS",
            source_type="rss",
            adapter_class="RSSAdapter",
            config={"url": "https://example.com/rss.xml"}
        )
        
        adapter = RSSAdapter(valid_config)
        self.assertTrue(adapter.validate_config())
        
        # 无效配置（缺少URL）
        invalid_config = DataSourceConfig(
            source_id="test_rss_invalid",
            name="无效RSS",
            source_type="rss",
            adapter_class="RSSAdapter",
            config={}
        )
        
        adapter = RSSAdapter(invalid_config)
        self.assertFalse(adapter.validate_config())
    
    def test_api_adapter_validation(self):
        """测试API适配器配置验证"""
        # 有效配置
        valid_config = DataSourceConfig(
            source_id="test_api",
            name="测试API",
            source_type="api",
            adapter_class="APIAdapter",
            config={"url": "https://api.example.com/news"}
        )
        
        adapter = APIAdapter(valid_config)
        self.assertTrue(adapter.validate_config())
        
        # 无效配置（缺少URL）
        invalid_config = DataSourceConfig(
            source_id="test_api_invalid",
            name="无效API",
            source_type="api",
            adapter_class="APIAdapter",
            config={}
        )
        
        adapter = APIAdapter(invalid_config)
        self.assertFalse(adapter.validate_config())
    
    @patch('requests.Session.get')
    def test_rss_adapter_fetch_data(self, mock_get):
        """测试RSS适配器数据获取"""
        # 模拟RSS响应
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # 模拟feedparser解析结果
        with patch('feedparser.parse') as mock_parse:
            mock_parse.return_value = Mock(
                bozo=False,
                entries=[
                    Mock(
                        title="测试新闻1",
                        description="测试内容1",
                        link="https://example.com/news1",
                        published_parsed=(2023, 12, 1, 10, 0, 0, 0, 0, 0)
                    ),
                    Mock(
                        title="测试新闻2",
                        description="测试内容2",
                        link="https://example.com/news2",
                        published_parsed=(2023, 12, 1, 11, 0, 0, 0, 0, 0)
                    )
                ]
            )
            
            config = DataSourceConfig(
                source_id="test_rss",
                name="测试RSS",
                source_type="rss",
                adapter_class="RSSAdapter",
                config={"url": "https://example.com/rss.xml"}
            )
            
            adapter = RSSAdapter(config)
            data = adapter.fetch_data()
            
            self.assertEqual(len(data), 2)
            self.assertIsInstance(data[0], UnifiedNewsData)
            self.assertEqual(data[0].title, "测试新闻1")
            self.assertEqual(data[0].source_name, "测试RSS")
    
    @patch('requests.Session.get')
    def test_api_adapter_fetch_data(self, mock_get):
        """测试API适配器数据获取"""
        # 模拟API响应
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "data": [
                {
                    "title": "API新闻1",
                    "content": "API内容1",
                    "url": "https://example.com/api-news1",
                    "publish_time": "2023-12-01T10:00:00Z"
                },
                {
                    "title": "API新闻2",
                    "content": "API内容2",
                    "url": "https://example.com/api-news2",
                    "publish_time": "2023-12-01T11:00:00Z"
                }
            ]
        }
        mock_get.return_value = mock_response
        
        config = DataSourceConfig(
            source_id="test_api",
            name="测试API",
            source_type="api",
            adapter_class="APIAdapter",
            config={"url": "https://api.example.com/news"}
        )
        
        adapter = APIAdapter(config)
        data = adapter.fetch_data()
        
        self.assertEqual(len(data), 2)
        self.assertIsInstance(data[0], UnifiedNewsData)
        self.assertEqual(data[0].title, "API新闻1")
        self.assertEqual(data[0].source_name, "测试API")


class TestDataSourceAPI(unittest.TestCase):
    """数据源API测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json')
        self.temp_file.write('[]')
        self.temp_file.close()
        
        # 创建API实例
        with patch('financial_analysis.data_source_api.data_source_registry') as mock_registry:
            mock_registry.return_value = DataSourceRegistry(config_file=self.temp_file.name)
            self.api = DataSourceAPI()
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_create_source(self):
        """测试创建数据源API"""
        source_data = {
            "source_id": "api_test_rss",
            "name": "API测试RSS",
            "source_type": "rss",
            "adapter_class": "RSSAdapter",
            "config": {"url": "https://example.com/rss.xml"}
        }
        
        with patch.object(self.api.registry, 'register_source', return_value=True):
            result = self.api.create_source(source_data)
            
            self.assertTrue(result["success"])
            self.assertEqual(result["source_id"], "api_test_rss")
    
    def test_create_source_missing_fields(self):
        """测试创建数据源时缺少必填字段"""
        incomplete_data = {
            "source_id": "incomplete_source",
            "name": "不完整的数据源"
            # 缺少 source_type 和 adapter_class
        }
        
        result = self.api.create_source(incomplete_data)
        
        self.assertFalse(result["success"])
        self.assertIn("缺少必填字段", result["error"])
    
    def test_list_sources(self):
        """测试列出数据源API"""
        mock_sources = [
            Mock(dict=lambda: {"source_id": "test1", "name": "测试1"}),
            Mock(dict=lambda: {"source_id": "test2", "name": "测试2"})
        ]
        
        with patch.object(self.api.registry, 'list_sources', return_value=mock_sources):
            with patch.object(self.api.registry, 'get_source_status', return_value=None):
                result = self.api.list_sources()
                
                self.assertTrue(result["success"])
                self.assertEqual(result["total"], 2)
                self.assertEqual(len(result["data"]), 2)


class TestHotNewsCollector(unittest.TestCase):
    """热点新闻收集器测试"""
    
    def test_new_system_initialization(self):
        """测试新系统初始化"""
        collector = HotNewsCollector(use_new_system=True)
        self.assertTrue(collector.use_new_system)
        self.assertIsNotNone(collector.registry)
    
    def test_legacy_system_initialization(self):
        """测试传统系统初始化"""
        with patch('financial_analysis.hot_news_collector.settings') as mock_settings:
            mock_settings.hot_news_channels = ""
            collector = HotNewsCollector(use_new_system=False)
            self.assertFalse(collector.use_new_system)
            self.assertIsNotNone(collector._channels)
    
    @patch('financial_analysis.hot_news_collector.data_source_registry')
    def test_collect_unified_news(self, mock_registry):
        """测试收集统一格式新闻"""
        # 模拟数据源和适配器
        mock_source = Mock()
        mock_source.source_id = "test_source"
        mock_source.name = "测试数据源"
        mock_source.last_fetch_time = None
        mock_source.fetch_interval = 300
        
        mock_adapter = Mock()
        mock_adapter.fetch_with_retry.return_value = [
            UnifiedNewsData(
                id="test1",
                title="测试新闻1",
                source_id="test_source",
                source_name="测试数据源",
                fetch_time=datetime.now()
            )
        ]
        
        mock_registry.list_sources.return_value = [mock_source]
        mock_registry.get_adapter.return_value = mock_adapter
        
        collector = HotNewsCollector(use_new_system=True)
        news_items = collector.collect_unified_news(force_refresh=True)
        
        self.assertEqual(len(news_items), 1)
        self.assertIsInstance(news_items[0], UnifiedNewsData)
        self.assertEqual(news_items[0].title, "测试新闻1")
    
    def test_add_data_source(self):
        """测试动态添加数据源"""
        with patch('financial_analysis.hot_news_collector.data_source_registry') as mock_registry:
            mock_registry.register_source.return_value = True
            
            collector = HotNewsCollector(use_new_system=True)
            
            source_config = {
                "source_id": "dynamic_test",
                "name": "动态测试",
                "source_type": "rss",
                "adapter_class": "RSSAdapter",
                "config": {"url": "https://example.com/rss.xml"}
            }
            
            success = collector.add_data_source(source_config)
            self.assertTrue(success)
    
    def test_add_data_source_legacy_system(self):
        """测试在传统系统中添加数据源（应该失败）"""
        collector = HotNewsCollector(use_new_system=False)
        
        source_config = {
            "source_id": "should_fail",
            "name": "应该失败",
            "source_type": "rss",
            "adapter_class": "RSSAdapter",
            "config": {"url": "https://example.com/rss.xml"}
        }
        
        success = collector.add_data_source(source_config)
        self.assertFalse(success)


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestDataSourceRegistry,
        TestDataSourceAdapters,
        TestDataSourceAPI,
        TestHotNewsCollector
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    print("运行动态数据源管理功能测试...")
    print("=" * 50)
    
    success = run_tests()
    
    print("=" * 50)
    if success:
        print("✓ 所有测试通过！")
    else:
        print("✗ 部分测试失败，请检查代码。")
    
    exit(0 if success else 1)


# 简单的集成测试
def integration_test():
    """简单的集成测试"""
    print("\n运行集成测试...")

    try:
        from financial_analysis.data_source_api import data_source_api

        # 测试基本功能
        print("1. 测试列出数据源...")
        result = data_source_api.list_sources()
        assert result["success"], "列出数据源失败"
        print(f"   ✓ 找到 {result['total']} 个数据源")

        # 测试添加数据源
        print("2. 测试添加数据源...")
        test_source = {
            "source_id": "integration_test_rss",
            "name": "集成测试RSS",
            "source_type": "rss",
            "adapter_class": "RSSAdapter",
            "config": {"url": "https://example.com/test.xml"},
            "enabled": False  # 禁用以避免实际请求
        }

        create_result = data_source_api.create_source(test_source)
        assert create_result["success"], f"添加数据源失败: {create_result.get('error')}"
        print("   ✓ 成功添加测试数据源")

        # 测试获取数据源
        print("3. 测试获取数据源...")
        get_result = data_source_api.get_source("integration_test_rss")
        assert get_result["success"], "获取数据源失败"
        print("   ✓ 成功获取数据源信息")

        # 测试删除数据源
        print("4. 测试删除数据源...")
        delete_result = data_source_api.delete_source("integration_test_rss")
        assert delete_result["success"], "删除数据源失败"
        print("   ✓ 成功删除测试数据源")

        print("✓ 集成测试通过！")
        return True

    except Exception as e:
        print(f"✗ 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("运行动态数据源管理功能测试...")
    print("=" * 50)

    # 运行单元测试
    unit_test_success = run_tests()

    # 运行集成测试
    integration_test_success = integration_test()

    print("=" * 50)
    overall_success = unit_test_success and integration_test_success

    if overall_success:
        print("✓ 所有测试通过！")
    else:
        print("✗ 部分测试失败，请检查代码。")

    exit(0 if overall_success else 1)
