"""
历史消息检测功能的单元测试

测试使用搜索工具判断历史消息的各种场景和边界情况。
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from typing import List

from financial_analysis.models import HotNewsItem
from financial_analysis.hot_news_analyzer import HotNewsAnalyzer
from financial_analysis.hot_news_manager import HotNewsManager


class TestHistoricalMessageDetection:
    """历史消息检测功能测试类"""
    
    def setup_method(self):
        """测试前的设置"""
        self.analyzer = HotNewsAnalyzer()
        self.current_time = datetime.now()
        
    def create_test_news_items(self) -> List[HotNewsItem]:
        """创建测试用的新闻条目"""
        return [
            HotNewsItem(
                news_id="test_1",
                title="最新财经新闻",
                content="今日股市表现良好",
                source="财经日报",
                channel_id="finance",
                url="https://example.com/1",
                publish_time=self.current_time - timedelta(hours=1),
                fetch_time=self.current_time
            ),
            HotNewsItem(
                news_id="test_2",
                title="历史新闻事件",
                content="这是一个历史事件",
                source="历史周刊",
                channel_id="history",
                url="https://example.com/2",
                publish_time=self.current_time - timedelta(days=5),
                fetch_time=self.current_time
            )
        ]
    
    def test_build_search_prompt(self):
        """测试搜索提示词构建"""
        news_items = self.create_test_news_items()
        prompt = self.analyzer._build_search_prompt(news_items)
        
        # 检查提示词包含必要的信息
        assert "请帮我检查以下新闻列表" in prompt
        assert "最新财经新闻" in prompt
        assert "历史新闻事件" in prompt
        assert "当前日期:" in prompt
        assert "历史消息判断标准:" in prompt
        assert "格式要求:" in prompt
    
    @patch('requests.post')
    def test_check_historical_news_with_search_success(self, mock_post):
        """测试成功的历史消息检查"""
        # 模拟成功的API响应
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            'result': """
1. 新闻编号: 1
2. 是否为历史消息: 否
3. 最初发布时间: 2024-01-20 10:00:00
4. 判断依据: 这是最近发布的新闻

1. 新闻编号: 2
2. 是否为历史消息: 是
3. 最初发布时间: 2024-01-15 14:00:00
4. 判断依据: 这是5天前的历史新闻
"""
        }
        mock_post.return_value = mock_response
        
        news_items = self.create_test_news_items()
        
        with patch('financial_analysis.config.settings.windmill_base_url', 'http://test.com'):
            with patch('financial_analysis.config.settings.windmill_token', 'test_token'):
                result = self.analyzer.check_historical_news_with_search(news_items)
        
        # 验证结果
        assert len(result) == 2
        assert result[0].is_historical == False  # 第一条不是历史消息
        assert result[1].is_historical == True   # 第二条是历史消息
        
        # 验证API调用
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert call_args[1]['json']['search'] == True
    
    @patch('requests.post')
    def test_check_historical_news_with_search_api_failure(self, mock_post):
        """测试API调用失败的情况"""
        # 模拟API调用失败
        mock_post.side_effect = Exception("API调用失败")
        
        news_items = self.create_test_news_items()
        
        with patch('financial_analysis.config.settings.windmill_base_url', 'http://test.com'):
            with patch('financial_analysis.config.settings.windmill_token', 'test_token'):
                result = self.analyzer.check_historical_news_with_search(news_items)
        
        # 验证降级处理：返回原始新闻列表，所有消息标记为非历史
        assert len(result) == 2
        assert all(not news.is_historical for news in result)
    
    def test_parse_search_result_valid_format(self):
        """测试解析有效格式的搜索结果"""
        news_items = self.create_test_news_items()
        search_result = """
1. 新闻编号: 1
2. 是否为历史消息: 否
3. 最初发布时间: 2024-01-20 10:00:00
4. 判断依据: 最近发布的新闻

1. 新闻编号: 2
2. 是否为历史消息: 是
3. 最初发布时间: 2024-01-15 14:00:00
4. 判断依据: 历史新闻
"""
        
        result = self.analyzer._parse_search_result(news_items, search_result)
        
        assert len(result) == 2
        assert result[0].is_historical == False
        assert result[1].is_historical == True
    
    def test_parse_search_result_invalid_format(self):
        """测试解析无效格式的搜索结果"""
        news_items = self.create_test_news_items()
        search_result = "这是一个无效的搜索结果格式"
        
        result = self.analyzer._parse_search_result(news_items, search_result)
        
        # 应该返回原始新闻列表，标记为非历史消息
        assert len(result) == 2
        assert all(not news.is_historical for news in result)
    
    def test_parse_search_result_partial_results(self):
        """测试部分解析结果的情况"""
        news_items = self.create_test_news_items()
        search_result = """
1. 新闻编号: 1
2. 是否为历史消息: 否
3. 最初发布时间: 2024-01-20 10:00:00
4. 判断依据: 最近发布的新闻
"""
        
        result = self.analyzer._parse_search_result(news_items, search_result)
        
        # 应该补全缺失的新闻项
        assert len(result) == 2
        assert result[0].is_historical == False
        assert result[1].is_historical == False  # 缺失的项默认为非历史
    
    @patch('financial_analysis.hot_news_analyzer.HotNewsAnalyzer.check_historical_news_with_search')
    def test_manager_check_historical_messages(self, mock_check):
        """测试管理器的历史消息检查功能"""
        # 模拟分析器的返回结果
        news_items = self.create_test_news_items()
        news_items[1].is_historical = True  # 标记第二条为历史消息
        mock_check.return_value = news_items
        
        manager = HotNewsManager()
        result = manager.check_historical_messages_with_search(news_items)
        
        assert result['success'] == True
        assert result['original_count'] == 2
        assert result['checked_count'] == 2
        assert result['historical_count'] == 1
        assert result['recent_count'] == 1
        assert 'processing_time' in result
    
    @patch('financial_analysis.hot_news_analyzer.HotNewsAnalyzer.check_historical_news_with_search')
    def test_manager_get_recent_messages_only(self, mock_check):
        """测试管理器获取最近消息功能"""
        # 模拟分析器的返回结果
        news_items = self.create_test_news_items()
        news_items[1].is_historical = True  # 标记第二条为历史消息
        mock_check.return_value = news_items
        
        manager = HotNewsManager()
        result = manager.get_recent_messages_only(news_items)
        
        # 应该只返回非历史消息
        assert len(result) == 1
        assert result[0].news_id == "test_1"
        assert not result[0].is_historical
    
    def test_empty_news_list(self):
        """测试空新闻列表的处理"""
        result = self.analyzer.check_historical_news_with_search([])
        assert result == []
        
        manager = HotNewsManager()
        manager_result = manager.check_historical_messages_with_search([])
        assert manager_result['success'] == True
        assert manager_result['original_count'] == 0
        assert manager_result['checked_count'] == 0
    
    @patch('financial_analysis.config.settings.windmill_base_url', None)
    @patch('financial_analysis.config.settings.windmill_token', None)
    def test_missing_windmill_config(self):
        """测试缺少Windmill配置的情况"""
        news_items = self.create_test_news_items()
        result = self.analyzer.check_historical_news_with_search(news_items)
        
        # 应该返回原始新闻列表，标记为非历史消息
        assert len(result) == 2
        assert all(not news.is_historical for news in result)
    
    def test_time_based_historical_detection(self):
        """测试基于时间的历史消息检测逻辑"""
        current_time = datetime.now()
        
        # 创建不同时间的新闻
        recent_news = HotNewsItem(
            news_id="recent",
            title="最近新闻",
            content="最近的内容",
            source="测试源",
            channel_id="test",
            url="https://example.com/recent",
            publish_time=current_time - timedelta(hours=1),
            fetch_time=current_time
        )
        
        old_news = HotNewsItem(
            news_id="old",
            title="历史新闻",
            content="历史内容",
            source="测试源",
            channel_id="test",
            url="https://example.com/old",
            publish_time=current_time - timedelta(days=5),
            fetch_time=current_time
        )
        
        # 模拟搜索结果，基于时间判断
        search_result = f"""
1. 新闻编号: 1
2. 是否为历史消息: 否
3. 最初发布时间: {(current_time - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S")}
4. 判断依据: 最近发布

1. 新闻编号: 2
2. 是否为历史消息: 是
3. 最初发布时间: {(current_time - timedelta(days=5)).strftime("%Y-%m-%d %H:%M:%S")}
4. 判断依据: 超过3天的历史消息
"""
        
        result = self.analyzer._parse_search_result([recent_news, old_news], search_result)
        
        assert len(result) == 2
        assert not result[0].is_historical  # 最近新闻
        assert result[1].is_historical      # 历史新闻
