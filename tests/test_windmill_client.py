"""
Windmill 客户端测试模块

测试 Windmill 异步客户端的各种功能，包括作业触发、状态轮询和结果获取。
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
import aiohttp

from financial_analysis.windmill_client import WindmillClient, windmill_client
from financial_analysis.config import settings


class TestWindmillClient:
    """Windmill 客户端测试类"""
    
    def setup_method(self):
        """测试方法设置"""
        self.client = WindmillClient(
            base_url="https://test.windmill.com",
            token="test_token",
            workspace="test_workspace"
        )
    
    @pytest.mark.asyncio
    async def test_trigger_job_success(self):
        """测试成功触发作业"""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text = AsyncMock(return_value="test-job-uuid-123")
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await self.client.trigger_job("test_folder", "test_script", {"param": "value"})
            
            assert result == "test-job-uuid-123"
            mock_post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_trigger_job_failure(self):
        """测试触发作业失败"""
        mock_response = AsyncMock()
        mock_response.status = 400
        mock_response.text = AsyncMock(return_value="Bad Request")
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await self.client.trigger_job("test_folder", "test_script")
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_wait_for_job_completion_success(self):
        """测试成功等待作业完成"""
        # 模拟第一次查询未完成，第二次查询完成
        mock_response_1 = AsyncMock()
        mock_response_1.status = 200
        mock_response_1.json = AsyncMock(return_value={"completed": False})
        
        mock_response_2 = AsyncMock()
        mock_response_2.status = 200
        mock_response_2.json = AsyncMock(return_value={
            "completed": True,
            "result": {"analysis": "测试分析结果"}
        })
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.side_effect = [mock_response_1, mock_response_2]
            
            result = await self.client.wait_for_job_completion("test-uuid", max_wait_time=10, poll_interval=0.1)
            
            assert result is not None
            assert result["completed"] is True
            assert result["result"]["analysis"] == "测试分析结果"
    
    @pytest.mark.asyncio
    async def test_wait_for_job_completion_timeout(self):
        """测试等待作业完成超时"""
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={"completed": False})
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_get.return_value.__aenter__.return_value = mock_response
            
            result = await self.client.wait_for_job_completion("test-uuid", max_wait_time=1, poll_interval=0.1)
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_execute_job_success(self):
        """测试成功执行完整作业流程"""
        # 模拟触发作业成功
        with patch.object(self.client, 'trigger_job', return_value="test-uuid") as mock_trigger:
            # 模拟等待完成成功
            with patch.object(self.client, 'wait_for_job_completion', 
                            return_value={"completed": True, "result": "success"}) as mock_wait:
                
                result = await self.client.execute_job("folder", "script", {"param": "value"})
                
                assert result is not None
                assert result["completed"] is True
                assert result["result"] == "success"
                
                mock_trigger.assert_called_once_with("folder", "script", {"param": "value"})
                mock_wait.assert_called_once_with("test-uuid", 300, 1)
    
    @pytest.mark.asyncio
    async def test_execute_job_trigger_failure(self):
        """测试执行作业时触发失败"""
        with patch.object(self.client, 'trigger_job', return_value=None):
            result = await self.client.execute_job("folder", "script")
            assert result is None
    
    @pytest.mark.asyncio
    async def test_generate_text_analysis_success(self):
        """测试成功生成文本分析"""
        mock_result = {
            "completed": True,
            "result": {
                "analysis": "这是一个测试分析结果"
            }
        }
        
        with patch.object(self.client, 'execute_job', return_value=mock_result):
            result = await self.client.generate_text_analysis(
                prompt="测试提示词",
                system_instruction="测试系统指令"
            )
            
            assert result == "这是一个测试分析结果"
    
    @pytest.mark.asyncio
    async def test_generate_text_analysis_failure(self):
        """测试生成文本分析失败"""
        with patch.object(self.client, 'execute_job', return_value=None):
            result = await self.client.generate_text_analysis("测试提示词")
            assert result is None
    
    def test_client_initialization_with_config(self):
        """测试使用配置初始化客户端"""
        # 临时修改配置
        original_base_url = settings.windmill_base_url
        original_token = settings.windmill_token
        original_workspace = settings.windmill_workspace
        
        try:
            settings.windmill_base_url = "https://config.windmill.com"
            settings.windmill_token = "config_token"
            settings.windmill_workspace = "config_workspace"
            
            client = WindmillClient()
            
            assert client.base_url == "https://config.windmill.com"
            assert client.token == "config_token"
            assert client.workspace == "config_workspace"
            
        finally:
            # 恢复原始配置
            settings.windmill_base_url = original_base_url
            settings.windmill_token = original_token
            settings.windmill_workspace = original_workspace
    
    def test_client_initialization_without_config(self):
        """测试无配置初始化客户端"""
        client = WindmillClient(base_url=None, token=None)
        
        # 应该使用配置中的值或None
        assert client.base_url == settings.windmill_base_url
        assert client.token == settings.windmill_token
    
    @pytest.mark.asyncio
    async def test_missing_config_handling(self):
        """测试缺少配置时的处理"""
        client = WindmillClient(base_url=None, token=None)
        
        # 触发作业应该返回None
        result = await client.trigger_job("folder", "script")
        assert result is None
        
        # 等待作业完成应该返回None
        result = await client.wait_for_job_completion("uuid")
        assert result is None


class TestGlobalWindmillClient:
    """全局 Windmill 客户端测试类"""
    
    def test_global_client_exists(self):
        """测试全局客户端实例存在"""
        assert windmill_client is not None
        assert isinstance(windmill_client, WindmillClient)
    
    @pytest.mark.asyncio
    async def test_global_client_functionality(self):
        """测试全局客户端功能"""
        # 这里只测试客户端是否可以调用，不测试实际网络请求
        assert hasattr(windmill_client, 'trigger_job')
        assert hasattr(windmill_client, 'wait_for_job_completion')
        assert hasattr(windmill_client, 'execute_job')
        assert hasattr(windmill_client, 'generate_text_analysis')


@pytest.mark.integration
class TestWindmillClientIntegration:
    """Windmill 客户端集成测试类（需要真实的 Windmill 服务）"""
    
    @pytest.mark.skip(reason="需要真实的 Windmill 服务配置")
    @pytest.mark.asyncio
    async def test_real_windmill_call(self):
        """测试真实的 Windmill 调用（需要配置真实的服务）"""
        # 这个测试需要真实的 Windmill 服务配置
        # 在实际环境中运行时取消 skip 装饰器
        
        if not settings.windmill_base_url or not settings.windmill_token:
            pytest.skip("缺少 Windmill 配置")
        
        result = await windmill_client.generate_text_analysis(
            prompt="请分析一下苹果公司的股票",
            system_instruction="你是一位专业的金融分析师"
        )
        
        assert result is not None
        assert isinstance(result, str)
        assert len(result) > 0
