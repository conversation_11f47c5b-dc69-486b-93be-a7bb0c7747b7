#!/usr/bin/env python3
"""
测试使用搜索工具判断历史消息的功能

这个脚本用于测试新实现的历史消息检查功能，通过调用 text_generation 接口
的搜索功能来判断消息是否为历史消息。
"""

import sys
import os
from datetime import datetime, timedelta
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis.models import HotNewsItem
from financial_analysis.hot_news_manager import hot_news_manager
from financial_analysis.hot_news_analyzer import HotNewsAnalyzer
from loguru import logger


def create_test_news_items() -> List[HotNewsItem]:
    """创建测试用的新闻条目"""
    current_time = datetime.now()
    
    test_news = [
        HotNewsItem(
            news_id="test_1",
            title="苹果公司发布iPhone 15系列手机",
            content="苹果公司在秋季发布会上正式发布了iPhone 15系列手机，包括iPhone 15、iPhone 15 Plus、iPhone 15 Pro和iPhone 15 Pro Max四款机型。",
            source="科技日报",
            channel_id="tech_news",
            url="https://example.com/news/1",
            publish_time=current_time - timedelta(days=1),
            fetch_time=current_time
        ),
        HotNewsItem(
            news_id="test_2", 
            title="中国央行宣布降准0.25个百分点",
            content="中国人民银行宣布，为支持实体经济发展，决定于2024年1月15日下调金融机构存款准备金率0.25个百分点。",
            source="财经网",
            channel_id="finance_news",
            url="https://example.com/news/2",
            publish_time=current_time - timedelta(days=2),
            fetch_time=current_time
        ),
        HotNewsItem(
            news_id="test_3",
            title="特斯拉股价大涨超过10%",
            content="特斯拉公司股价今日大涨超过10%，主要受益于第四季度交付量超预期的消息。",
            source="华尔街日报",
            channel_id="stock_news", 
            url="https://example.com/news/3",
            publish_time=current_time - timedelta(days=5),
            fetch_time=current_time
        ),
        HotNewsItem(
            news_id="test_4",
            title="新冠疫情最新进展",
            content="世界卫生组织发布新冠疫情最新报告，全球新增确诊病例持续下降。",
            source="健康时报",
            channel_id="health_news",
            url="https://example.com/news/4", 
            publish_time=current_time - timedelta(days=10),
            fetch_time=current_time
        ),
        HotNewsItem(
            news_id="test_5",
            title="比特币价格突破50000美元",
            content="比特币价格今日突破50000美元大关，创下近期新高，市场情绪乐观。",
            source="币圈快讯",
            channel_id="crypto_news",
            url="https://example.com/news/5",
            publish_time=current_time - timedelta(hours=6),
            fetch_time=current_time
        )
    ]
    
    return test_news


def test_historical_check_with_analyzer():
    """测试使用分析器直接检查历史消息"""
    logger.info("=== 测试使用分析器直接检查历史消息 ===")
    
    try:
        # 创建测试数据
        test_news = create_test_news_items()
        logger.info(f"创建了 {len(test_news)} 条测试新闻")
        
        # 创建分析器实例
        analyzer = HotNewsAnalyzer()
        
        # 使用搜索工具检查历史消息
        checked_news = analyzer.check_historical_news_with_search(test_news)
        
        # 输出结果
        logger.info("=== 检查结果 ===")
        for i, news in enumerate(checked_news, 1):
            status = "历史消息" if news.is_historical else "最近消息"
            logger.info(f"{i}. {news.title[:30]}... - {status}")
            logger.info(f"   发布时间: {news.publish_time}")
            logger.info(f"   是否历史: {news.is_historical}")
            logger.info("")
        
        # 统计结果
        historical_count = sum(1 for news in checked_news if news.is_historical)
        recent_count = len(checked_news) - historical_count
        
        logger.info(f"统计结果: 总计 {len(checked_news)} 条，历史消息 {historical_count} 条，最近消息 {recent_count} 条")
        
        return checked_news
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        return []


def test_historical_check_with_manager():
    """测试使用管理器检查历史消息"""
    logger.info("=== 测试使用管理器检查历史消息 ===")
    
    try:
        # 创建测试数据
        test_news = create_test_news_items()
        logger.info(f"创建了 {len(test_news)} 条测试新闻")
        
        # 使用管理器检查历史消息
        result = hot_news_manager.check_historical_messages_with_search(test_news)
        
        # 输出结果
        logger.info("=== 检查结果 ===")
        logger.info(f"检查状态: {result['success']}")
        logger.info(f"处理消息: {result['message']}")
        logger.info(f"原始数量: {result['original_count']}")
        logger.info(f"检查数量: {result['checked_count']}")
        logger.info(f"历史消息: {result['historical_count']}")
        logger.info(f"最近消息: {result['recent_count']}")
        logger.info(f"处理时间: {result['processing_time']}秒")
        
        if result['success'] and 'checked_news' in result:
            logger.info("\n=== 详细结果 ===")
            for i, news in enumerate(result['checked_news'], 1):
                status = "历史消息" if news.is_historical else "最近消息"
                logger.info(f"{i}. {news.title[:30]}... - {status}")
        
        return result
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        return {"success": False, "error": str(e)}


def test_get_recent_messages():
    """测试获取最近消息功能"""
    logger.info("=== 测试获取最近消息功能 ===")
    
    try:
        # 创建测试数据
        test_news = create_test_news_items()
        logger.info(f"创建了 {len(test_news)} 条测试新闻")
        
        # 获取最近消息
        recent_messages = hot_news_manager.get_recent_messages_only(test_news)
        
        # 输出结果
        logger.info("=== 最近消息列表 ===")
        for i, news in enumerate(recent_messages, 1):
            logger.info(f"{i}. {news.title}")
            logger.info(f"   发布时间: {news.publish_time}")
            logger.info("")
        
        logger.info(f"从 {len(test_news)} 条消息中筛选出 {len(recent_messages)} 条最近消息")
        
        return recent_messages
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        return []


def main():
    """主函数"""
    logger.info("开始测试历史消息检查功能")
    
    # 测试1: 使用分析器直接检查
    test_historical_check_with_analyzer()
    
    print("\n" + "="*50 + "\n")
    
    # 测试2: 使用管理器检查
    test_historical_check_with_manager()
    
    print("\n" + "="*50 + "\n")
    
    # 测试3: 获取最近消息
    test_get_recent_messages()
    
    logger.info("测试完成")


if __name__ == "__main__":
    main()
