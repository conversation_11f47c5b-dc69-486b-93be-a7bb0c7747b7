#!/usr/bin/env python3
"""
Windmill 异步客户端集成测试

测试 Windmill 异步客户端与现有分析引擎的集成功能。
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis.windmill_client import WindmillClient, windmill_client
from financial_analysis.analysis import AnalysisEngine
from financial_analysis.config import settings


async def test_windmill_client_basic():
    """测试 Windmill 客户端基本功能"""
    print("=== 测试 Windmill 客户端基本功能 ===")
    
    # 创建测试客户端
    client = WindmillClient(
        base_url="https://test.windmill.com",
        token="test_token",
        workspace="test_workspace"
    )
    
    print(f"✓ 客户端创建成功")
    print(f"  基础URL: {client.base_url}")
    print(f"  工作空间: {client.workspace}")
    print(f"  令牌: {'已配置' if client.token else '未配置'}")
    
    # 测试配置验证
    if not client.base_url or not client.token:
        print("✗ 配置不完整")
        return False
    
    print("✓ 配置验证通过")
    return True


async def test_windmill_client_mock_call():
    """测试模拟 Windmill 调用"""
    print("\n=== 测试模拟 Windmill 调用 ===")
    
    client = WindmillClient(
        base_url="https://mock.windmill.com",
        token="mock_token",
        workspace="mock_workspace"
    )
    
    # 尝试触发作业（预期失败）
    try:
        job_uuid = await client.trigger_job(
            folder="test",
            script="test",
            payload={"test": "data"}
        )
        
        if job_uuid:
            print(f"✗ 意外成功: {job_uuid}")
            return False
        else:
            print("✓ 按预期失败：无效URL导致触发失败")
            return True
            
    except Exception as e:
        print(f"✓ 按预期异常: {type(e).__name__}")
        return True


def test_analysis_engine_integration():
    """测试分析引擎集成"""
    print("\n=== 测试分析引擎集成 ===")
    
    try:
        # 创建分析引擎实例
        engine = AnalysisEngine()
        print("✓ 分析引擎创建成功")
        
        # 检查是否有异步方法
        if hasattr(engine, '_call_windmill_analysis_async'):
            print("✓ 异步 Windmill 调用方法存在")
        else:
            print("✗ 异步 Windmill 调用方法不存在")
            return False
        
        # 检查同步包装方法
        if hasattr(engine, '_call_windmill_analysis'):
            print("✓ 同步包装方法存在")
        else:
            print("✗ 同步包装方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 分析引擎集成测试失败: {e}")
        return False


def test_configuration():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    print("当前 Windmill 配置:")
    print(f"  基础URL: {settings.windmill_base_url}")
    print(f"  工作空间: {settings.windmill_workspace}")
    print(f"  文件夹: {settings.windmill_folder}")
    print(f"  脚本: {settings.windmill_script}")
    print(f"  令牌: {'已配置' if settings.windmill_token else '未配置'}")
    
    # 检查必要配置
    required_configs = [
        ('windmill_base_url', settings.windmill_base_url),
        ('windmill_workspace', settings.windmill_workspace),
        ('windmill_folder', settings.windmill_folder),
        ('windmill_script', settings.windmill_script),
    ]
    
    missing_configs = []
    for name, value in required_configs:
        if not value:
            missing_configs.append(name)
    
    if missing_configs:
        print(f"✗ 缺少配置: {', '.join(missing_configs)}")
        return False
    else:
        print("✓ 必要配置完整")
        return True


async def test_global_client():
    """测试全局客户端实例"""
    print("\n=== 测试全局客户端实例 ===")
    
    try:
        # 检查全局实例
        if windmill_client is None:
            print("✗ 全局客户端实例不存在")
            return False
        
        print("✓ 全局客户端实例存在")
        print(f"  类型: {type(windmill_client).__name__}")
        print(f"  基础URL: {windmill_client.base_url}")
        print(f"  工作空间: {windmill_client.workspace}")
        
        # 检查方法
        methods = ['trigger_job', 'wait_for_job_completion', 'execute_job', 'generate_text_analysis']
        for method in methods:
            if hasattr(windmill_client, method):
                print(f"✓ 方法 {method} 存在")
            else:
                print(f"✗ 方法 {method} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 全局客户端测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("Windmill 异步客户端集成测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(await test_windmill_client_basic())
    test_results.append(await test_windmill_client_mock_call())
    test_results.append(test_analysis_engine_integration())
    test_results.append(test_configuration())
    test_results.append(await test_global_client())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！")
        return 0
    else:
        print(f"✗ {total - passed} 个测试失败")
        return 1


if __name__ == "__main__":
    # 运行测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
