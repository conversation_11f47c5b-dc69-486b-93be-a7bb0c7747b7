"""
热点信息缓存管理模块

提供热点信息的缓存存储、过期管理和清理功能。
支持内存缓存和持久化存储。
"""

import json
import time
import threading
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
from pathlib import Path
from loguru import logger

from .models import HotNewsItem, HotNewsCache
from .config import settings


class HotNewsCacheManager:
    """热点信息缓存管理器"""
    
    def __init__(self, cache_dir: str = "cache"):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录路径
        """
        self._memory_cache = {}  # 内存缓存
        self._cache_dir = Path(cache_dir)
        self._cache_dir.mkdir(exist_ok=True)
        self._cache_file = self._cache_dir / "hot_news_cache.json"
        self._lock = threading.RLock()
        
        # 加载持久化缓存
        self._load_persistent_cache()
        
        # 启动清理线程
        self._start_cleanup_thread()
        
        logger.info("热点信息缓存管理器初始化完成")
    
    def set(self, key: str, data: Any, expire_seconds: Optional[int] = None) -> bool:
        """
        设置缓存
        
        Args:
            key: 缓存键
            data: 缓存数据
            expire_seconds: 过期时间（秒），None表示使用默认配置
            
        Returns:
            是否设置成功
        """
        try:
            with self._lock:
                expire_seconds = expire_seconds or settings.hot_news_cache_duration
                current_time = datetime.now()
                expire_time = current_time + timedelta(seconds=expire_seconds)
                
                cache_item = HotNewsCache(
                    cache_key=key,
                    data=data,
                    created_time=current_time,
                    expire_time=expire_time,
                    access_count=0,
                    last_access_time=None
                )
                
                self._memory_cache[key] = cache_item
                
                # 异步保存到文件
                self._save_persistent_cache_async()
                
                logger.debug(f"设置缓存: {key}")
                return True
                
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {str(e)}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存
        
        Args:
            key: 缓存键
            
        Returns:
            缓存数据，如果不存在或已过期返回None
        """
        try:
            with self._lock:
                if key not in self._memory_cache:
                    return None
                
                cache_item = self._memory_cache[key]
                current_time = datetime.now()
                
                # 检查是否过期
                if current_time > cache_item.expire_time:
                    del self._memory_cache[key]
                    logger.debug(f"缓存已过期: {key}")
                    return None
                
                # 更新访问信息
                cache_item.access_count += 1
                cache_item.last_access_time = current_time
                
                logger.debug(f"获取缓存: {key}")
                return cache_item.data
                
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {str(e)}")
            return None
    
    def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        try:
            with self._lock:
                if key in self._memory_cache:
                    del self._memory_cache[key]
                    logger.debug(f"删除缓存: {key}")
                    return True
                return False
                
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {str(e)}")
            return False
    
    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在且未过期
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        return self.get(key) is not None
    
    def clear_expired(self) -> int:
        """
        清理过期缓存
        
        Returns:
            清理的缓存数量
        """
        try:
            with self._lock:
                current_time = datetime.now()
                expired_keys = []
                
                for key, cache_item in self._memory_cache.items():
                    if current_time > cache_item.expire_time:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del self._memory_cache[key]
                
                if expired_keys:
                    logger.info(f"清理了 {len(expired_keys)} 个过期缓存")
                
                return len(expired_keys)
                
        except Exception as e:
            logger.error(f"清理过期缓存失败: {str(e)}")
            return 0
    
    def clear_all(self) -> bool:
        """
        清理所有缓存
        
        Returns:
            是否清理成功
        """
        try:
            with self._lock:
                cache_count = len(self._memory_cache)
                self._memory_cache.clear()
                
                # 删除持久化文件
                if self._cache_file.exists():
                    self._cache_file.unlink()
                
                logger.info(f"清理了所有缓存，共 {cache_count} 个")
                return True
                
        except Exception as e:
            logger.error(f"清理所有缓存失败: {str(e)}")
            return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息
        """
        try:
            with self._lock:
                current_time = datetime.now()
                total_count = len(self._memory_cache)
                expired_count = 0
                total_access = 0
                
                for cache_item in self._memory_cache.values():
                    if current_time > cache_item.expire_time:
                        expired_count += 1
                    total_access += cache_item.access_count
                
                return {
                    "total_count": total_count,
                    "active_count": total_count - expired_count,
                    "expired_count": expired_count,
                    "total_access": total_access,
                    "cache_dir": str(self._cache_dir),
                    "cache_file_exists": self._cache_file.exists()
                }
                
        except Exception as e:
            logger.error(f"获取缓存统计失败: {str(e)}")
            return {}
    
    def cache_news_list(self, news_items: List[HotNewsItem], cache_key: str = "latest_hot_news") -> bool:
        """
        缓存新闻列表
        
        Args:
            news_items: 新闻列表
            cache_key: 缓存键
            
        Returns:
            是否缓存成功
        """
        try:
            # 转换为可序列化的格式
            news_data = [news.dict() for news in news_items]
            return self.set(cache_key, news_data)
            
        except Exception as e:
            logger.error(f"缓存新闻列表失败: {str(e)}")
            return False
    
    def get_cached_news_list(self, cache_key: str = "latest_hot_news") -> List[HotNewsItem]:
        """
        获取缓存的新闻列表
        
        Args:
            cache_key: 缓存键
            
        Returns:
            新闻列表
        """
        try:
            news_data = self.get(cache_key)
            if not news_data:
                return []
            
            # 转换回HotNewsItem对象
            news_items = []
            for item_data in news_data:
                news_item = HotNewsItem(**item_data)
                news_items.append(news_item)
            
            return news_items
            
        except Exception as e:
            logger.error(f"获取缓存新闻列表失败: {str(e)}")
            return []
    
    def _load_persistent_cache(self):
        """加载持久化缓存"""
        try:
            if not self._cache_file.exists():
                return
            
            with open(self._cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            current_time = datetime.now()
            loaded_count = 0
            
            for key, item_data in cache_data.items():
                try:
                    # 解析时间字段
                    item_data['created_time'] = datetime.fromisoformat(item_data['created_time'])
                    item_data['expire_time'] = datetime.fromisoformat(item_data['expire_time'])
                    if item_data.get('last_access_time'):
                        item_data['last_access_time'] = datetime.fromisoformat(item_data['last_access_time'])
                    
                    cache_item = HotNewsCache(**item_data)
                    
                    # 检查是否过期
                    if current_time <= cache_item.expire_time:
                        self._memory_cache[key] = cache_item
                        loaded_count += 1
                    
                except Exception as e:
                    logger.warning(f"加载缓存项失败 {key}: {str(e)}")
                    continue
            
            logger.info(f"从持久化文件加载了 {loaded_count} 个缓存项")
            
        except Exception as e:
            logger.error(f"加载持久化缓存失败: {str(e)}")
    
    def _save_persistent_cache_async(self):
        """异步保存持久化缓存"""
        def save_cache():
            try:
                with self._lock:
                    cache_data = {}
                    current_time = datetime.now()
                    
                    for key, cache_item in self._memory_cache.items():
                        # 只保存未过期的缓存
                        if current_time <= cache_item.expire_time:
                            item_dict = cache_item.dict()
                            # 转换时间为ISO格式字符串
                            item_dict['created_time'] = cache_item.created_time.isoformat()
                            item_dict['expire_time'] = cache_item.expire_time.isoformat()
                            if cache_item.last_access_time:
                                item_dict['last_access_time'] = cache_item.last_access_time.isoformat()
                            cache_data[key] = item_dict
                    
                    with open(self._cache_file, 'w', encoding='utf-8') as f:
                        json.dump(cache_data, f, ensure_ascii=False, indent=2)
                    
                    logger.debug(f"保存了 {len(cache_data)} 个缓存项到持久化文件")
                    
            except Exception as e:
                logger.error(f"保存持久化缓存失败: {str(e)}")
        
        # 在后台线程中保存
        threading.Thread(target=save_cache, daemon=True).start()
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(300)  # 每5分钟清理一次
                    self.clear_expired()
                except Exception as e:
                    logger.error(f"清理线程异常: {str(e)}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logger.info("缓存清理线程已启动")


# 全局缓存管理器实例
cache_manager = HotNewsCacheManager()
