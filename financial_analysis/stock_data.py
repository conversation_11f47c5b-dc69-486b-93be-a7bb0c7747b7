"""
股票数据获取模块

负责从各种数据源获取股票的基本信息、价格数据和技术指标。
支持多个交易所的股票代码，包括上交所、深交所、港交所、纳斯达克等。
"""

import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import pandas as pd
import yfinance as yf
from loguru import logger

# 尝试导入akshare，如果失败则设置为None
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    ak = None
    AKSHARE_AVAILABLE = False
    logger.warning("akshare不可用，中国股票数据将使用模拟数据")

from .models import StockInfo, StockPrice, TechnicalIndicators
from .utils import normalize_stock_symbol, get_exchange_suffix, safe_float, safe_int
from .config import settings


class StockDataProvider:
    """股票数据提供者类"""
    
    def __init__(self):
        """初始化股票数据提供者"""
        self._cache = {}  # 简单的内存缓存
        self._cache_timeout = settings.data_cache_duration
        logger.info("股票数据提供者初始化完成")
    
    def get_stock_info(self, symbol: str, exchange: Optional[str] = None) -> Optional[StockInfo]:
        """
        获取股票基本信息
        
        Args:
            symbol: 股票代码
            exchange: 交易所代码
            
        Returns:
            股票基本信息，如果获取失败返回None
        """
        try:
            normalized_symbol = normalize_stock_symbol(symbol, exchange)
            cache_key = f"info_{normalized_symbol}"
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                logger.debug(f"从缓存获取股票信息: {normalized_symbol}")
                return self._cache[cache_key]['data']
            
            logger.info(f"获取股票基本信息: {normalized_symbol}")
            
            # 根据交易所选择数据源
            if exchange in ['SSE', 'SZSE'] or self._is_china_stock(normalized_symbol):
                # 中国A股使用akshare或模拟数据
                stock_info = self._get_china_stock_info(normalized_symbol)
            else:
                # 其他市场使用yfinance
                stock_info = self._get_international_stock_info(normalized_symbol, exchange)
            
            # 缓存结果
            if stock_info:
                self._cache[cache_key] = {
                    'data': stock_info,
                    'timestamp': time.time()
                }
            
            return stock_info
            
        except Exception as e:
            logger.error(f"获取股票信息失败 {symbol}: {str(e)}")
            return None
    
    def get_stock_prices(self, symbol: str, days: int = 30, exchange: Optional[str] = None) -> List[StockPrice]:
        """
        获取股票历史价格数据
        
        Args:
            symbol: 股票代码
            days: 获取天数
            exchange: 交易所代码
            
        Returns:
            股票价格数据列表
        """
        try:
            normalized_symbol = normalize_stock_symbol(symbol, exchange)
            cache_key = f"prices_{normalized_symbol}_{days}"
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                logger.debug(f"从缓存获取股票价格数据: {normalized_symbol}")
                return self._cache[cache_key]['data']
            
            logger.info(f"获取股票价格数据: {normalized_symbol}, 天数: {days}")
            
            # 根据交易所选择数据源
            if exchange in ['SSE', 'SZSE'] or self._is_china_stock(normalized_symbol):
                # 中国A股使用akshare或模拟数据
                prices = self._get_china_stock_prices(normalized_symbol, days)
            else:
                # 其他市场使用yfinance
                prices = self._get_international_stock_prices(normalized_symbol, days, exchange)
            
            # 缓存结果
            if prices:
                self._cache[cache_key] = {
                    'data': prices,
                    'timestamp': time.time()
                }
            
            return prices
            
        except Exception as e:
            logger.error(f"获取股票价格数据失败 {symbol}: {str(e)}")
            return []
    
    def get_technical_indicators(self, symbol: str, exchange: Optional[str] = None) -> Optional[TechnicalIndicators]:
        """
        获取股票技术指标
        
        Args:
            symbol: 股票代码
            exchange: 交易所代码
            
        Returns:
            技术指标数据，如果获取失败返回None
        """
        try:
            normalized_symbol = normalize_stock_symbol(symbol, exchange)
            cache_key = f"indicators_{normalized_symbol}"
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                logger.debug(f"从缓存获取技术指标: {normalized_symbol}")
                return self._cache[cache_key]['data']
            
            logger.info(f"计算技术指标: {normalized_symbol}")
            
            # 获取价格数据用于计算技术指标
            prices = self.get_stock_prices(symbol, 60, exchange)  # 获取60天数据用于计算指标
            if not prices:
                return None
            
            # 转换为DataFrame进行计算
            df = pd.DataFrame([{
                'date': price.date,
                'open': price.open_price,
                'high': price.high_price,
                'low': price.low_price,
                'close': price.close_price,
                'volume': price.volume
            } for price in prices])
            
            df = df.sort_values('date')
            
            # 计算技术指标
            indicators = self._calculate_technical_indicators(df, normalized_symbol)
            
            # 缓存结果
            if indicators:
                self._cache[cache_key] = {
                    'data': indicators,
                    'timestamp': time.time()
                }
            
            return indicators
            
        except Exception as e:
            logger.error(f"获取技术指标失败 {symbol}: {str(e)}")
            return None

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache:
            return False

        cache_time = self._cache[cache_key]['timestamp']
        return time.time() - cache_time < self._cache_timeout

    def _get_china_stock_info(self, symbol: str) -> Optional[StockInfo]:
        """获取中国A股股票信息"""
        try:
            if not AKSHARE_AVAILABLE:
                # 返回模拟数据
                return self._get_mock_china_stock_info(symbol)

            # 使用akshare获取股票基本信息
            stock_info_df = ak.stock_individual_info_em(symbol=symbol)

            if stock_info_df.empty:
                return None

            # 解析股票信息
            info_dict = dict(zip(stock_info_df['item'], stock_info_df['value']))

            # 确定交易所
            exchange = 'SSE' if symbol.startswith('6') else 'SZSE'

            return StockInfo(
                symbol=symbol,
                name=info_dict.get('股票简称', ''),
                exchange=exchange,
                currency='CNY',
                sector=info_dict.get('所属行业', None),
                market_cap=safe_float(info_dict.get('总市值', 0))
            )

        except Exception as e:
            logger.warning(f"使用akshare获取中国股票信息失败: {str(e)}")
            return self._get_mock_china_stock_info(symbol)

    def _get_international_stock_info(self, symbol: str, exchange: Optional[str]) -> Optional[StockInfo]:
        """获取国际股票信息"""
        try:
            # 构造yfinance符号
            yf_symbol = symbol + get_exchange_suffix(exchange or 'NASDAQ')

            # 使用yfinance获取股票信息
            ticker = yf.Ticker(yf_symbol)
            info = ticker.info

            if not info:
                return None

            return StockInfo(
                symbol=symbol,
                name=info.get('longName', info.get('shortName', '')),
                exchange=exchange or 'NASDAQ',
                currency=info.get('currency', 'USD'),
                sector=info.get('sector', None),
                market_cap=safe_float(info.get('marketCap', 0))
            )

        except Exception as e:
            logger.warning(f"使用yfinance获取国际股票信息失败: {str(e)}")
            return None

    def _get_china_stock_prices(self, symbol: str, days: int) -> List[StockPrice]:
        """获取中国A股价格数据"""
        try:
            if not AKSHARE_AVAILABLE:
                # 返回模拟数据
                return self._get_mock_china_stock_prices(symbol, days)

            # 计算日期范围
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')

            # 使用akshare获取历史数据
            df = ak.stock_zh_a_hist(symbol=symbol, period="daily", start_date=start_date, end_date=end_date)

            if df.empty:
                return []

            prices = []
            for _, row in df.iterrows():
                price = StockPrice(
                    symbol=symbol,
                    date=pd.to_datetime(row['日期']),
                    open_price=safe_float(row['开盘']),
                    high_price=safe_float(row['最高']),
                    low_price=safe_float(row['最低']),
                    close_price=safe_float(row['收盘']),
                    volume=safe_int(row['成交量']),
                    turnover=safe_float(row.get('成交额', 0))
                )
                prices.append(price)

            return prices

        except Exception as e:
            logger.warning(f"使用akshare获取中国股票价格失败: {str(e)}")
            return self._get_mock_china_stock_prices(symbol, days)

    def _get_international_stock_prices(self, symbol: str, days: int, exchange: Optional[str]) -> List[StockPrice]:
        """获取国际股票价格数据"""
        try:
            # 构造yfinance符号
            yf_symbol = symbol + get_exchange_suffix(exchange or 'NASDAQ')

            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # 使用yfinance获取历史数据
            ticker = yf.Ticker(yf_symbol)
            df = ticker.history(start=start_date, end=end_date)

            if df.empty:
                return []

            prices = []
            for date, row in df.iterrows():
                price = StockPrice(
                    symbol=symbol,
                    date=date.to_pydatetime(),
                    open_price=safe_float(row['Open']),
                    high_price=safe_float(row['High']),
                    low_price=safe_float(row['Low']),
                    close_price=safe_float(row['Close']),
                    volume=safe_int(row['Volume']),
                    turnover=None  # yfinance不提供成交额
                )
                prices.append(price)

            return prices

        except Exception as e:
            logger.warning(f"使用yfinance获取国际股票价格失败: {str(e)}")
            return []

    def _calculate_technical_indicators(self, df: pd.DataFrame, symbol: str) -> Optional[TechnicalIndicators]:
        """计算技术指标"""
        try:
            if df.empty or len(df) < 20:
                logger.warning(f"数据不足，无法计算技术指标: {symbol}")
                return None

            # 获取最新日期
            latest_date = df['date'].max()

            # 计算移动平均线
            df['ma5'] = df['close'].rolling(window=5).mean()
            df['ma10'] = df['close'].rolling(window=10).mean()
            df['ma20'] = df['close'].rolling(window=20).mean()
            df['ma60'] = df['close'].rolling(window=60).mean()

            # 计算RSI
            df['rsi'] = self._calculate_rsi(df['close'])

            # 计算MACD
            df['macd'] = self._calculate_macd(df['close'])

            # 计算KDJ
            kdj_k, kdj_d, kdj_j = self._calculate_kdj(df)
            df['kdj_k'] = kdj_k
            df['kdj_d'] = kdj_d
            df['kdj_j'] = kdj_j

            # 获取最新的指标值
            latest_row = df[df['date'] == latest_date].iloc[-1]

            return TechnicalIndicators(
                symbol=symbol,
                date=latest_date,
                ma5=safe_float(latest_row.get('ma5')),
                ma10=safe_float(latest_row.get('ma10')),
                ma20=safe_float(latest_row.get('ma20')),
                ma60=safe_float(latest_row.get('ma60')),
                rsi=safe_float(latest_row.get('rsi')),
                macd=safe_float(latest_row.get('macd')),
                kdj_k=safe_float(latest_row.get('kdj_k')),
                kdj_d=safe_float(latest_row.get('kdj_d')),
                kdj_j=safe_float(latest_row.get('kdj_j'))
            )

        except Exception as e:
            logger.error(f"计算技术指标失败: {str(e)}")
            return None

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.Series:
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        return macd

    def _calculate_kdj(self, df: pd.DataFrame, period: int = 9) -> tuple:
        """计算KDJ指标"""
        low_min = df['low'].rolling(window=period).min()
        high_max = df['high'].rolling(window=period).max()

        rsv = (df['close'] - low_min) / (high_max - low_min) * 100

        k = rsv.ewm(com=2).mean()
        d = k.ewm(com=2).mean()
        j = 3 * k - 2 * d

        return k, d, j

    def _get_mock_china_stock_info(self, symbol: str) -> StockInfo:
        """获取模拟的中国股票信息"""
        # 根据股票代码生成模拟信息
        stock_names = {
            "000001": "平安银行",
            "000002": "万科A",
            "600036": "招商银行",
            "600519": "贵州茅台"
        }

        sectors = {
            "000001": "银行业",
            "000002": "房地产业",
            "600036": "银行业",
            "600519": "食品饮料业"
        }

        name = stock_names.get(symbol, f"股票{symbol}")
        sector = sectors.get(symbol, "其他行业")
        exchange = 'SSE' if symbol.startswith('6') else 'SZSE'

        return StockInfo(
            symbol=symbol,
            name=name,
            exchange=exchange,
            currency='CNY',
            sector=sector,
            market_cap=150000000000  # 1500亿
        )

    def _get_mock_china_stock_prices(self, symbol: str, days: int) -> List[StockPrice]:
        """获取模拟的中国股票价格数据"""
        prices = []
        base_price = 12.0  # 基础价格

        for i in range(days):
            date = datetime.now() - timedelta(days=days-1-i)

            # 生成模拟价格（简单的随机波动）
            import random
            random.seed(hash(symbol + str(i)))  # 确保可重复

            change = random.uniform(-0.5, 0.5)
            current_price = base_price + change + i * 0.01

            price = StockPrice(
                symbol=symbol,
                date=date,
                open_price=current_price - 0.1,
                high_price=current_price + 0.2,
                low_price=current_price - 0.2,
                close_price=current_price,
                volume=1000000 + random.randint(-100000, 100000),
                turnover=(current_price * (1000000 + random.randint(-100000, 100000)))
            )
            prices.append(price)

        return prices

    def _is_china_stock(self, symbol: str) -> bool:
        """判断是否为中国股票"""
        # 中国A股代码特征：6开头（上交所）或0、3开头（深交所）
        return (symbol.startswith('6') or
                symbol.startswith('0') or
                symbol.startswith('3')) and symbol.isdigit() and len(symbol) == 6
