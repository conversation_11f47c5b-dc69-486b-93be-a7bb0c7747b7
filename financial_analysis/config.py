"""
项目配置模块

负责管理项目的所有配置参数，包括API密钥、数据源配置、日志配置等。
使用pydantic-settings进行配置管理，支持从环境变量和.env文件读取配置。
"""

from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """项目配置类"""
    
    # 大模型配置（已迁移到Windmill生成文本接口）
    # 保留model配置用于兼容性，但实际使用Windmill接口
    gemini_model: str = Field(default="gemini-pro", description="大模型名称（仅用于标识）")
    
    # Windmill配置
    windmill_base_url: Optional[str] = Field(default=None, description="Windmill服务基础URL")
    windmill_token: Optional[str] = Field(default=None, description="Windmill访问令牌")
    windmill_workspace: str = Field(default="my_workspace", description="Windmill工作空间")
    windmill_folder: str = Field(default="gemini", description="Windmill文件夹")
    windmill_script: str = Field(default="js_structured_output", description="Windmill脚本名称")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: str = Field(default="logs/financial_analysis.log", description="日志文件路径")
    
    # 数据源配置
    default_exchange: str = Field(default="SSE", description="默认交易所")
    data_cache_duration: int = Field(default=300, description="数据缓存时间（秒）")
    
    # 分析配置
    analysis_days: int = Field(default=30, description="分析的历史天数")
    news_search_days: int = Field(default=7, description="新闻搜索的天数范围")

    # 热点信息配置
    hot_news_enabled: bool = Field(default=True, description="是否启用热点信息功能")
    hot_news_fetch_interval: int = Field(default=300, description="热点信息获取间隔（秒）")
    hot_news_cache_duration: int = Field(default=1800, description="热点信息缓存时间（秒）")
    hot_news_max_items: int = Field(default=100, description="单次获取的最大新闻数量")
    hot_news_history_check_days: int = Field(default=3, description="历史信息检查天数")

    # 热点信息推送配置
    hot_news_push_enabled: bool = Field(default=True, description="是否启用热点信息推送")
    hot_news_push_interval: int = Field(default=600, description="推送间隔（秒）")
    hot_news_push_batch_size: int = Field(default=10, description="单次推送的最大数量")
    hot_news_min_importance: str = Field(default="medium", description="推送的最低重要程度：high/medium/low")

    # 热点信息数据源配置
    hot_news_channels: str = Field(default="", description="热点信息数据源配置（JSON格式）")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()
