"""
主程序入口模块

提供命令行接口供用户输入股票代码并获取分析报告。
支持多种输出格式和交互模式。
"""

import sys
import argparse
import json
from datetime import datetime
from typing import Optional
from loguru import logger

from .analysis import AnalysisEngine
from .utils import setup_logging, format_currency
from .config import settings


class FinancialAnalysisCLI:
    """金融分析命令行接口类"""
    
    def __init__(self):
        """初始化CLI"""
        setup_logging()
        self.engine = AnalysisEngine()
        logger.info("金融分析CLI初始化完成")
    
    def run(self, args: Optional[list] = None):
        """
        运行CLI程序
        
        Args:
            args: 命令行参数列表，如果为None则使用sys.argv
        """
        try:
            # 解析命令行参数
            parser = self._create_parser()
            parsed_args = parser.parse_args(args)
            
            # 根据命令执行相应操作
            if parsed_args.command == 'analyze':
                self._handle_analyze_command(parsed_args)
            elif parsed_args.command == 'batch':
                self._handle_batch_command(parsed_args)
            elif parsed_args.command == 'interactive':
                self._handle_interactive_command(parsed_args)
            else:
                parser.print_help()
                
        except KeyboardInterrupt:
            logger.info("用户中断程序执行")
            print("\n程序已退出")
        except Exception as e:
            logger.error(f"程序执行失败: {str(e)}")
            print(f"错误: {str(e)}")
            sys.exit(1)
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="金融证券分析工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  financial-analysis analyze 000001              # 分析平安银行
  financial-analysis analyze AAPL --exchange NASDAQ  # 分析苹果股票
  financial-analysis batch stocks.txt           # 批量分析
  financial-analysis interactive                # 交互模式
            """
        )
        
        # 添加子命令
        subparsers = parser.add_subparsers(dest='command', help='可用命令')
        
        # analyze命令
        analyze_parser = subparsers.add_parser('analyze', help='分析单个股票')
        analyze_parser.add_argument('symbol', help='股票代码')
        analyze_parser.add_argument('--exchange', '-e', help='交易所代码 (SSE/SZSE/HK/NASDAQ/NYSE)')
        analyze_parser.add_argument('--output', '-o', choices=['text', 'json'], default='text', help='输出格式')
        analyze_parser.add_argument('--file', '-f', help='输出到文件')
        analyze_parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
        
        # batch命令
        batch_parser = subparsers.add_parser('batch', help='批量分析股票')
        batch_parser.add_argument('file', help='包含股票代码的文件路径')
        batch_parser.add_argument('--output', '-o', choices=['text', 'json'], default='text', help='输出格式')
        batch_parser.add_argument('--output-dir', '-d', help='输出目录')
        
        # interactive命令
        interactive_parser = subparsers.add_parser('interactive', help='交互模式')
        interactive_parser.add_argument('--output', '-o', choices=['text', 'json'], default='text', help='默认输出格式')
        
        return parser
    
    def _handle_analyze_command(self, args):
        """处理analyze命令"""
        logger.info(f"开始分析股票: {args.symbol}")
        print(f"正在分析股票 {args.symbol}...")
        
        # 生成分析报告
        report = self.engine.generate_analysis_report(args.symbol, args.exchange)
        
        if not report:
            print(f"错误: 无法生成 {args.symbol} 的分析报告")
            return
        
        # 输出结果
        if args.output == 'json':
            output = self._format_json_output(report)
        else:
            output = self._format_text_output(report, args.verbose)
        
        if args.file:
            self._save_to_file(output, args.file)
            print(f"分析报告已保存到: {args.file}")
        else:
            print(output)
    
    def _handle_batch_command(self, args):
        """处理batch命令"""
        try:
            # 读取股票代码列表
            with open(args.file, 'r', encoding='utf-8') as f:
                symbols = [line.strip() for line in f if line.strip()]
            
            logger.info(f"开始批量分析 {len(symbols)} 只股票")
            print(f"开始批量分析 {len(symbols)} 只股票...")
            
            results = []
            for i, symbol in enumerate(symbols, 1):
                print(f"[{i}/{len(symbols)}] 分析 {symbol}...")
                
                # 解析股票代码和交易所
                parts = symbol.split(':')
                stock_symbol = parts[0]
                exchange = parts[1] if len(parts) > 1 else None
                
                # 生成分析报告
                report = self.engine.generate_analysis_report(stock_symbol, exchange)
                if report:
                    results.append(report)
                    print(f"  ✓ {report.stock_info.name} - {report.overall_rating}")
                else:
                    print(f"  ✗ 分析失败")
            
            # 输出结果
            if args.output == 'json':
                output = self._format_batch_json_output(results)
            else:
                output = self._format_batch_text_output(results)
            
            if args.output_dir:
                import os
                os.makedirs(args.output_dir, exist_ok=True)
                filename = f"batch_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{'json' if args.output == 'json' else 'txt'}"
                filepath = os.path.join(args.output_dir, filename)
                self._save_to_file(output, filepath)
                print(f"批量分析报告已保存到: {filepath}")
            else:
                print(output)
                
        except FileNotFoundError:
            print(f"错误: 找不到文件 {args.file}")
        except Exception as e:
            print(f"错误: 批量分析失败 - {str(e)}")
    
    def _handle_interactive_command(self, args):
        """处理interactive命令"""
        print("欢迎使用金融证券分析工具 - 交互模式")
        print("输入股票代码进行分析，输入 'quit' 或 'exit' 退出")
        print("格式: 股票代码[:交易所] (例如: 000001:SZSE 或 AAPL)")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n请输入股票代码: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("再见!")
                    break
                
                if not user_input:
                    continue
                
                # 解析输入
                parts = user_input.split(':')
                symbol = parts[0]
                exchange = parts[1] if len(parts) > 1 else None
                
                print(f"正在分析 {symbol}...")
                
                # 生成分析报告
                report = self.engine.generate_analysis_report(symbol, exchange)
                
                if report:
                    output = self._format_text_output(report, verbose=False)
                    print(output)
                else:
                    print(f"错误: 无法生成 {symbol} 的分析报告")
                    
            except KeyboardInterrupt:
                print("\n再见!")
                break
            except Exception as e:
                print(f"错误: {str(e)}")
                continue

    def _format_text_output(self, report, verbose: bool = False) -> str:
        """格式化文本输出"""
        lines = []

        # 标题
        lines.append("=" * 60)
        lines.append(f"股票分析报告 - {report.stock_info.name} ({report.symbol})")
        lines.append("=" * 60)

        # 基本信息
        lines.append("\n【基本信息】")
        lines.append(f"股票名称: {report.stock_info.name}")
        lines.append(f"股票代码: {report.symbol}")
        lines.append(f"交易所: {report.stock_info.exchange}")
        if report.stock_info.sector:
            lines.append(f"所属行业: {report.stock_info.sector}")
        if report.stock_info.market_cap:
            lines.append(f"市值: {format_currency(report.stock_info.market_cap, report.stock_info.currency)}")

        # 价格信息
        lines.append("\n【价格信息】")
        lines.append(f"当前价格: {report.current_price:.2f} {report.stock_info.currency}")
        change_symbol = "+" if report.price_change >= 0 else ""
        lines.append(f"价格变化: {change_symbol}{report.price_change:.2f} ({change_symbol}{report.price_change_percent:.2f}%)")

        # 技术指标
        lines.append("\n【技术指标】")
        if report.technical_indicators.ma5:
            lines.append(f"5日均线: {report.technical_indicators.ma5:.2f}")
        if report.technical_indicators.ma20:
            lines.append(f"20日均线: {report.technical_indicators.ma20:.2f}")
        if report.technical_indicators.rsi:
            lines.append(f"RSI: {report.technical_indicators.rsi:.2f}")
        if report.technical_indicators.macd:
            lines.append(f"MACD: {report.technical_indicators.macd:.2f}")

        # 趋势分析
        lines.append("\n【趋势分析】")
        lines.append(report.trend_analysis)

        # 支撑阻力
        if report.support_resistance.get('support') or report.support_resistance.get('resistance'):
            lines.append("\n【支撑阻力】")
            if report.support_resistance.get('support'):
                lines.append(f"支撑位: {report.support_resistance['support']:.2f}")
            if report.support_resistance.get('resistance'):
                lines.append(f"阻力位: {report.support_resistance['resistance']:.2f}")

        # 新闻分析
        lines.append("\n【新闻分析】")
        lines.append(f"新闻数量: {len(report.news_items)} 条")
        lines.append(f"整体情感: {self._translate_sentiment(report.news_sentiment)}")
        if verbose and report.news_items:
            lines.append("\n最新新闻:")
            for i, news in enumerate(report.news_items[:3], 1):
                lines.append(f"  {i}. {news.title} ({self._translate_sentiment(news.sentiment)})")

        # 新闻摘要
        if report.news_summary:
            lines.append(f"\n新闻摘要: {report.news_summary}")

        # 投资建议
        lines.append("\n【投资建议】")
        lines.append(f"综合评级: {report.overall_rating}")
        lines.append(f"风险等级: {report.risk_level}")
        lines.append(f"投资建议: {report.investment_advice}")

        # AI分析
        if verbose and report.ai_analysis:
            lines.append("\n【AI分析】")
            lines.append(report.ai_analysis)

        # 分析时间
        lines.append(f"\n分析时间: {report.analysis_date.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("=" * 60)

        return "\n".join(lines)

    def _format_json_output(self, report) -> str:
        """格式化JSON输出"""
        # 将报告转换为字典
        report_dict = {
            "symbol": report.symbol,
            "stock_info": {
                "name": report.stock_info.name,
                "exchange": report.stock_info.exchange,
                "currency": report.stock_info.currency,
                "sector": report.stock_info.sector,
                "market_cap": report.stock_info.market_cap
            },
            "analysis_date": report.analysis_date.isoformat(),
            "price_data": {
                "current_price": report.current_price,
                "price_change": report.price_change,
                "price_change_percent": report.price_change_percent
            },
            "technical_indicators": {
                "ma5": report.technical_indicators.ma5,
                "ma10": report.technical_indicators.ma10,
                "ma20": report.technical_indicators.ma20,
                "ma60": report.technical_indicators.ma60,
                "rsi": report.technical_indicators.rsi,
                "macd": report.technical_indicators.macd,
                "kdj_k": report.technical_indicators.kdj_k,
                "kdj_d": report.technical_indicators.kdj_d,
                "kdj_j": report.technical_indicators.kdj_j
            },
            "analysis": {
                "trend_analysis": report.trend_analysis,
                "support_resistance": report.support_resistance,
                "fundamental_analysis": report.fundamental_analysis
            },
            "news_analysis": {
                "news_count": len(report.news_items),
                "overall_sentiment": report.news_sentiment,
                "news_summary": report.news_summary,
                "news_items": [
                    {
                        "title": news.title,
                        "source": news.source,
                        "publish_time": news.publish_time.isoformat(),
                        "sentiment": news.sentiment,
                        "url": news.url
                    }
                    for news in report.news_items
                ]
            },
            "investment_advice": {
                "overall_rating": report.overall_rating,
                "risk_level": report.risk_level,
                "advice": report.investment_advice
            },
            "ai_analysis": report.ai_analysis
        }

        return json.dumps(report_dict, ensure_ascii=False, indent=2)

    def _format_batch_text_output(self, reports) -> str:
        """格式化批量分析的文本输出"""
        lines = []

        lines.append("=" * 80)
        lines.append(f"批量股票分析报告 - 共 {len(reports)} 只股票")
        lines.append("=" * 80)

        # 汇总统计
        buy_count = sum(1 for r in reports if r.overall_rating == "买入")
        hold_count = sum(1 for r in reports if r.overall_rating == "持有")
        sell_count = sum(1 for r in reports if r.overall_rating == "卖出")

        lines.append("\n【汇总统计】")
        lines.append(f"买入推荐: {buy_count} 只")
        lines.append(f"持有推荐: {hold_count} 只")
        lines.append(f"卖出推荐: {sell_count} 只")

        # 详细列表
        lines.append("\n【详细列表】")
        lines.append(f"{'序号':<4} {'代码':<8} {'名称':<12} {'当前价格':<10} {'涨跌幅':<8} {'评级':<6} {'风险':<4}")
        lines.append("-" * 70)

        for i, report in enumerate(reports, 1):
            change_str = f"{report.price_change_percent:+.2f}%"
            lines.append(
                f"{i:<4} {report.symbol:<8} {report.stock_info.name[:10]:<12} "
                f"{report.current_price:<10.2f} {change_str:<8} "
                f"{report.overall_rating:<6} {report.risk_level:<4}"
            )

        lines.append("\n分析时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        lines.append("=" * 80)

        return "\n".join(lines)

    def _format_batch_json_output(self, reports) -> str:
        """格式化批量分析的JSON输出"""
        batch_result = {
            "analysis_time": datetime.now().isoformat(),
            "total_count": len(reports),
            "summary": {
                "buy_count": sum(1 for r in reports if r.overall_rating == "买入"),
                "hold_count": sum(1 for r in reports if r.overall_rating == "持有"),
                "sell_count": sum(1 for r in reports if r.overall_rating == "卖出")
            },
            "reports": []
        }

        for report in reports:
            report_summary = {
                "symbol": report.symbol,
                "name": report.stock_info.name,
                "current_price": report.current_price,
                "price_change_percent": report.price_change_percent,
                "overall_rating": report.overall_rating,
                "risk_level": report.risk_level,
                "news_sentiment": report.news_sentiment
            }
            batch_result["reports"].append(report_summary)

        return json.dumps(batch_result, ensure_ascii=False, indent=2)

    def _translate_sentiment(self, sentiment: str) -> str:
        """翻译情感标签"""
        sentiment_map = {
            'positive': '正面',
            'negative': '负面',
            'neutral': '中性'
        }
        return sentiment_map.get(sentiment, sentiment)

    def _save_to_file(self, content: str, filepath: str):
        """保存内容到文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            raise


def main():
    """主函数入口"""
    cli = FinancialAnalysisCLI()
    cli.run()


if __name__ == "__main__":
    main()
