"""
热点信息管理器模块

整合热点信息的收集、分析、缓存、推送等功能。
提供统一的接口进行热点信息的管理和查询。
"""

import json
import time
import threading
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from loguru import logger

from .models import HotNewsItem, HotNewsChannel
from .config import settings
from .hot_news_collector import HotNewsCollector
from .hot_news_analyzer import HotNewsAnalyzer
from .hot_news_cache import cache_manager
from .hot_news_pusher import HotNewsPusher


class HotNewsManager:
    """热点信息管理器类"""
    
    def __init__(self):
        """初始化热点信息管理器"""
        self.collector = HotNewsCollector()
        self.analyzer = HotNewsAnalyzer()
        self.pusher = HotNewsPusher()
        self._news_storage = {}  # 新闻存储
        self._running = False
        self._worker_thread = None
        logger.info("热点信息管理器初始化完成")
    
    def start_auto_collection(self):
        """启动自动收集服务"""
        try:
            if self._running:
                logger.warning("自动收集服务已在运行")
                return
            
            if not settings.hot_news_enabled:
                logger.info("热点信息功能已禁用")
                return
            
            self._running = True
            self._worker_thread = threading.Thread(target=self._auto_collection_worker, daemon=True)
            self._worker_thread.start()
            
            logger.info("热点信息自动收集服务已启动")
            
        except Exception as e:
            logger.error(f"启动自动收集服务失败: {str(e)}")
    
    def stop_auto_collection(self):
        """停止自动收集服务"""
        try:
            self._running = False
            if self._worker_thread and self._worker_thread.is_alive():
                self._worker_thread.join(timeout=5)
            
            logger.info("热点信息自动收集服务已停止")
            
        except Exception as e:
            logger.error(f"停止自动收集服务失败: {str(e)}")
    
    def collect_and_process_news(self, force_refresh: bool = False) -> Dict[str, Any]:
        """
        收集并处理热点信息
        
        Args:
            force_refresh: 是否强制刷新
            
        Returns:
            处理结果统计
        """
        try:
            logger.info("开始收集和处理热点信息")
            start_time = time.time()
            
            # 1. 收集热点信息
            raw_news = self.collector.collect_hot_news(force_refresh)
            if not raw_news:
                return {"success": True, "message": "没有收集到新的热点信息", "collected_count": 0}
            
            logger.info(f"收集到 {len(raw_news)} 条原始新闻")
            
            # 2. 使用搜索工具检查历史消息
            search_checked_news = self.analyzer.check_historical_news_with_search(raw_news)
            logger.info(f"完成 {len(search_checked_news)} 条新闻的历史信息搜索检查")

            # 3. 分析新闻（对未标记为历史的新闻进行详细分析）
            non_historical_news = [news for news in search_checked_news if not news.is_historical]
            if non_historical_news:
                analyzed_news = self.analyzer.analyze_news_batch(non_historical_news)
                logger.info(f"完成 {len(analyzed_news)} 条非历史新闻的详细分析")
            else:
                analyzed_news = []
                logger.info("所有新闻都被标记为历史消息，跳过详细分析")

            # 4. 过滤历史信息（最终过滤）
            filtered_news = self.analyzer.filter_historical_news(analyzed_news)
            logger.info(f"最终过滤后剩余 {len(filtered_news)} 条新闻")
            
            # 5. 存储新闻
            stored_count = self._store_news_items(filtered_news)

            # 6. 缓存最新新闻
            cache_manager.cache_news_list(filtered_news, "latest_hot_news")

            # 7. 推送新闻
            push_result = self.pusher.push_news_batch(filtered_news)

            processing_time = time.time() - start_time

            # 统计历史消息数量
            historical_count = len([news for news in search_checked_news if news.is_historical])

            result = {
                "success": True,
                "message": "热点信息处理完成",
                "collected_count": len(raw_news),
                "search_checked_count": len(search_checked_news),
                "historical_count": historical_count,
                "analyzed_count": len(analyzed_news),
                "filtered_count": len(filtered_news),
                "stored_count": stored_count,
                "push_result": push_result,
                "processing_time": round(processing_time, 2)
            }
            
            logger.info(f"热点信息处理完成: {result['message']}")
            return result
            
        except Exception as e:
            logger.error(f"收集和处理热点信息失败: {str(e)}")
            return {"success": False, "message": f"处理失败: {str(e)}"}

    def check_historical_messages_with_search(self, news_items: List[HotNewsItem]) -> Dict[str, Any]:
        """
        使用搜索工具检查消息是否为历史消息

        Args:
            news_items: 待检查的新闻列表

        Returns:
            检查结果字典，包含更新后的新闻列表和统计信息
        """
        try:
            start_time = time.time()

            if not news_items:
                return {
                    "success": True,
                    "message": "没有需要检查的消息",
                    "original_count": 0,
                    "checked_count": 0,
                    "historical_count": 0,
                    "recent_count": 0,
                    "processing_time": 0
                }

            logger.info(f"开始使用搜索工具检查 {len(news_items)} 条消息的历史性")

            # 使用搜索工具检查历史消息
            checked_news = self.analyzer.check_historical_news_with_search(news_items)

            # 统计结果
            historical_count = sum(1 for news in checked_news if news.is_historical)
            recent_count = len(checked_news) - historical_count

            processing_time = time.time() - start_time

            result = {
                "success": True,
                "message": "历史消息检查完成",
                "original_count": len(news_items),
                "checked_count": len(checked_news),
                "historical_count": historical_count,
                "recent_count": recent_count,
                "checked_news": checked_news,
                "processing_time": round(processing_time, 2)
            }

            logger.info(f"历史消息检查完成: 原始 {len(news_items)} 条，历史消息 {historical_count} 条，最近消息 {recent_count} 条")
            return result

        except Exception as e:
            logger.error(f"检查历史消息失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "original_count": len(news_items) if news_items else 0,
                "checked_count": 0,
                "historical_count": 0,
                "recent_count": 0
            }

    def get_recent_messages_only(self, news_items: List[HotNewsItem]) -> List[HotNewsItem]:
        """
        获取最近的消息（过滤掉历史消息）

        Args:
            news_items: 新闻列表

        Returns:
            过滤后的最近消息列表
        """
        try:
            # 使用搜索工具检查历史消息
            check_result = self.check_historical_messages_with_search(news_items)

            if check_result["success"]:
                recent_messages = [news for news in check_result["checked_news"] if not news.is_historical]
                logger.info(f"从 {len(news_items)} 条消息中筛选出 {len(recent_messages)} 条最近消息")
                return recent_messages
            else:
                logger.error("历史消息检查失败，返回原始消息列表")
                return news_items

        except Exception as e:
            logger.error(f"获取最近消息失败: {str(e)}")
            return news_items
    
    def get_news_by_id(self, news_id: str) -> Optional[HotNewsItem]:
        """
        根据新闻ID获取详细信息
        
        Args:
            news_id: 新闻唯一标识
            
        Returns:
            新闻详细信息，如果不存在返回None
        """
        try:
            # 首先从内存存储中查找
            if news_id in self._news_storage:
                logger.debug(f"从内存获取新闻详情: {news_id}")
                return self._news_storage[news_id]
            
            # 从缓存中查找
            cached_news = cache_manager.get_cached_news_list("latest_hot_news")
            for news in cached_news:
                if news.news_id == news_id:
                    logger.debug(f"从缓存获取新闻详情: {news_id}")
                    return news
            
            # 从历史缓存中查找
            for cache_key in ["processed_hot_news", "all_hot_news"]:
                cached_news = cache_manager.get_cached_news_list(cache_key)
                for news in cached_news:
                    if news.news_id == news_id:
                        logger.debug(f"从历史缓存获取新闻详情: {news_id}")
                        return news
            
            logger.warning(f"未找到新闻: {news_id}")
            return None
            
        except Exception as e:
            logger.error(f"获取新闻详情失败 {news_id}: {str(e)}")
            return None
    
    def get_latest_news(self, limit: int = 20, category: Optional[str] = None, 
                       importance: Optional[str] = None) -> List[HotNewsItem]:
        """
        获取最新热点信息
        
        Args:
            limit: 返回数量限制
            category: 新闻分类过滤
            importance: 重要程度过滤
            
        Returns:
            最新热点信息列表
        """
        try:
            # 从缓存获取最新新闻
            cached_news = cache_manager.get_cached_news_list("latest_hot_news")
            
            # 应用过滤条件
            filtered_news = cached_news
            
            if category:
                filtered_news = [news for news in filtered_news if news.category == category]
            
            if importance:
                filtered_news = [news for news in filtered_news if news.importance_level == importance]
            
            # 按发布时间排序
            filtered_news.sort(key=lambda x: x.publish_time, reverse=True)
            
            # 限制数量
            if len(filtered_news) > limit:
                filtered_news = filtered_news[:limit]
            
            logger.debug(f"获取最新新闻: {len(filtered_news)} 条")
            return filtered_news
            
        except Exception as e:
            logger.error(f"获取最新新闻失败: {str(e)}")
            return []
    
    def search_news(self, keyword: str, limit: int = 20) -> List[HotNewsItem]:
        """
        搜索热点信息
        
        Args:
            keyword: 搜索关键词
            limit: 返回数量限制
            
        Returns:
            搜索结果列表
        """
        try:
            # 从缓存获取所有新闻
            all_news = cache_manager.get_cached_news_list("latest_hot_news")
            
            # 搜索匹配的新闻
            matched_news = []
            keyword_lower = keyword.lower()
            
            for news in all_news:
                # 在标题、内容、关键词中搜索
                if (keyword_lower in news.title.lower() or
                    (news.content and keyword_lower in news.content.lower()) or
                    any(keyword_lower in kw.lower() for kw in news.keywords)):
                    matched_news.append(news)
            
            # 按相关度和时间排序
            matched_news.sort(key=lambda x: (
                self._calculate_relevance_score(x, keyword),
                x.publish_time
            ), reverse=True)
            
            # 限制数量
            if len(matched_news) > limit:
                matched_news = matched_news[:limit]
            
            logger.info(f"搜索关键词 '{keyword}' 找到 {len(matched_news)} 条结果")
            return matched_news
            
        except Exception as e:
            logger.error(f"搜索新闻失败: {str(e)}")
            return []
    
    def get_news_statistics(self) -> Dict[str, Any]:
        """
        获取新闻统计信息
        
        Returns:
            统计信息
        """
        try:
            cached_news = cache_manager.get_cached_news_list("latest_hot_news")
            
            # 基础统计
            total_count = len(cached_news)
            
            # 按分类统计
            category_stats = {}
            importance_stats = {}
            sentiment_stats = {}
            
            for news in cached_news:
                # 分类统计
                category = news.category or "未分类"
                category_stats[category] = category_stats.get(category, 0) + 1
                
                # 重要程度统计
                importance = news.importance_level or "medium"
                importance_stats[importance] = importance_stats.get(importance, 0) + 1
                
                # 情感统计
                sentiment = news.sentiment or "neutral"
                sentiment_stats[sentiment] = sentiment_stats.get(sentiment, 0) + 1
            
            # 推送统计
            push_stats = self.pusher.get_push_stats()
            
            # 缓存统计
            cache_stats = cache_manager.get_cache_stats()
            
            return {
                "total_news": total_count,
                "category_distribution": category_stats,
                "importance_distribution": importance_stats,
                "sentiment_distribution": sentiment_stats,
                "push_statistics": push_stats,
                "cache_statistics": cache_stats,
                "last_update": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取新闻统计失败: {str(e)}")
            return {}
    
    def _auto_collection_worker(self):
        """自动收集工作线程"""
        logger.info("自动收集工作线程已启动")
        
        while self._running:
            try:
                # 执行收集和处理
                result = self.collect_and_process_news()
                
                if result.get("success"):
                    logger.info(f"自动收集完成: {result.get('message')}")
                else:
                    logger.error(f"自动收集失败: {result.get('message')}")
                
                # 等待下次收集
                time.sleep(settings.hot_news_fetch_interval)
                
            except Exception as e:
                logger.error(f"自动收集工作线程异常: {str(e)}")
                time.sleep(60)  # 出错时等待1分钟再重试
        
        logger.info("自动收集工作线程已退出")
    
    def _store_news_items(self, news_items: List[HotNewsItem]) -> int:
        """存储新闻条目"""
        try:
            stored_count = 0
            
            for news in news_items:
                self._news_storage[news.news_id] = news
                stored_count += 1
            
            # 限制存储数量，删除最旧的新闻
            if len(self._news_storage) > 1000:
                # 按时间排序，删除最旧的200条
                sorted_news = sorted(
                    self._news_storage.items(),
                    key=lambda x: x[1].fetch_time
                )
                
                for i in range(200):
                    del self._news_storage[sorted_news[i][0]]
            
            logger.debug(f"存储了 {stored_count} 条新闻")
            return stored_count
            
        except Exception as e:
            logger.error(f"存储新闻失败: {str(e)}")
            return 0
    
    def _calculate_relevance_score(self, news: HotNewsItem, keyword: str) -> float:
        """计算新闻与关键词的相关度评分"""
        try:
            score = 0.0
            keyword_lower = keyword.lower()
            
            # 标题匹配权重最高
            if keyword_lower in news.title.lower():
                score += 10.0
            
            # 关键词匹配
            for kw in news.keywords:
                if keyword_lower in kw.lower():
                    score += 5.0
            
            # 内容匹配
            if news.content and keyword_lower in news.content.lower():
                score += 3.0
            
            # 重要程度加权
            importance_weights = {"high": 3.0, "medium": 2.0, "low": 1.0}
            score *= importance_weights.get(news.importance_level, 2.0)
            
            return score
            
        except Exception as e:
            logger.error(f"计算相关度评分失败: {str(e)}")
            return 0.0


# 全局热点信息管理器实例
hot_news_manager = HotNewsManager()
