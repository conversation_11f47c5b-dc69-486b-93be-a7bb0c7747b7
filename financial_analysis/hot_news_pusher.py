"""
热点信息推送模块

通过Windmill接口将热点信息推送给用户。
支持批量推送、推送状态管理和推送历史记录。
"""

import json
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import requests
from loguru import logger

from .models import HotNewsItem
from .config import settings
from .hot_news_cache import cache_manager


class HotNewsPusher:
    """热点信息推送器类"""
    
    def __init__(self):
        """初始化热点信息推送器"""
        self._push_history = {}  # 推送历史记录
        self._last_push_time = None
        logger.info("热点信息推送器初始化完成")
    
    def push_news_batch(self, news_items: List[HotNewsItem], force_push: bool = False) -> Dict[str, Any]:
        """
        批量推送热点信息
        
        Args:
            news_items: 待推送的新闻列表
            force_push: 是否强制推送，忽略推送间隔限制
            
        Returns:
            推送结果统计
        """
        try:
            if not settings.hot_news_push_enabled:
                logger.info("热点信息推送功能已禁用")
                return {"success": False, "message": "推送功能已禁用"}
            
            # 检查推送间隔
            if not force_push and not self._can_push_now():
                logger.info("未到推送时间，跳过推送")
                return {"success": False, "message": "未到推送时间"}
            
            # 过滤待推送的新闻
            pending_news = self._filter_pending_news(news_items)
            if not pending_news:
                logger.info("没有待推送的新闻")
                return {"success": True, "message": "没有待推送的新闻", "pushed_count": 0}
            
            # 限制推送数量
            batch_size = settings.hot_news_push_batch_size
            if len(pending_news) > batch_size:
                pending_news = pending_news[:batch_size]
                logger.info(f"限制推送数量为 {batch_size} 条")
            
            # 执行推送
            push_results = []
            success_count = 0
            
            for news in pending_news:
                try:
                    result = self._push_single_news(news)
                    push_results.append(result)
                    
                    if result.get("success", False):
                        success_count += 1
                        # 更新推送状态
                        news.is_pushed = True
                        news.push_time = datetime.now()
                        
                        # 记录推送历史
                        self._record_push_history(news, result)
                    
                    # 避免推送过于频繁
                    time.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"推送单条新闻失败 {news.news_id}: {str(e)}")
                    push_results.append({"success": False, "error": str(e)})
            
            # 更新最后推送时间
            self._last_push_time = datetime.now()
            
            # 缓存推送结果
            self._cache_push_results(push_results)
            
            result = {
                "success": True,
                "message": f"推送完成，成功 {success_count}/{len(pending_news)} 条",
                "pushed_count": success_count,
                "total_count": len(pending_news),
                "results": push_results
            }
            
            logger.info(f"批量推送完成: {result['message']}")
            return result
            
        except Exception as e:
            logger.error(f"批量推送失败: {str(e)}")
            return {"success": False, "message": f"推送失败: {str(e)}"}
    
    def _push_single_news(self, news_item: HotNewsItem) -> Dict[str, Any]:
        """
        推送单条热点信息
        
        Args:
            news_item: 待推送的新闻
            
        Returns:
            推送结果
        """
        try:
            if not settings.windmill_base_url or not settings.windmill_token:
                logger.warning("Windmill配置不完整，模拟推送")
                return self._mock_push_result(news_item)
            
            # 构造推送消息
            message = self._build_push_message(news_item)
            
            # 调用Windmill推送接口
            url = f"{settings.windmill_base_url}/api/w/{settings.windmill_workspace}/jobs/run/p/f/{settings.windmill_folder}/push_notification"
            
            headers = {
                'Authorization': f'Bearer {settings.windmill_token}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                "message": message,
                "news_id": news_item.news_id,
                "title": news_item.title,
                "url": news_item.url,
                "importance": news_item.importance_level,
                "category": news_item.category,
                "timestamp": news_item.publish_time.isoformat()
            }
            
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            
            return {
                "success": True,
                "news_id": news_item.news_id,
                "message": "推送成功",
                "response": result
            }
            
        except Exception as e:
            logger.error(f"推送单条新闻失败: {str(e)}")
            return {
                "success": False,
                "news_id": news_item.news_id,
                "message": f"推送失败: {str(e)}"
            }
    
    def _build_push_message(self, news_item: HotNewsItem) -> str:
        """构造推送消息"""
        try:
            # 重要程度标识
            importance_emoji = {
                "high": "🔥",
                "medium": "📢",
                "low": "ℹ️"
            }
            
            emoji = importance_emoji.get(news_item.importance_level, "📢")
            
            # 构造消息内容
            message_parts = [
                f"{emoji} 热点资讯",
                f"标题: {news_item.title}",
                f"来源: {news_item.source}",
                f"时间: {news_item.publish_time.strftime('%Y-%m-%d %H:%M')}"
            ]
            
            if news_item.summary:
                message_parts.append(f"摘要: {news_item.summary}")
            
            if news_item.keywords:
                keywords_str = " ".join([f"#{keyword}" for keyword in news_item.keywords[:3]])
                message_parts.append(f"关键词: {keywords_str}")
            
            if news_item.url:
                message_parts.append(f"详情: {news_item.url}")
            
            return "\n".join(message_parts)
            
        except Exception as e:
            logger.error(f"构造推送消息失败: {str(e)}")
            return f"热点资讯: {news_item.title}"
    
    def _mock_push_result(self, news_item: HotNewsItem) -> Dict[str, Any]:
        """模拟推送结果（当Windmill不可用时）"""
        logger.info(f"模拟推送: {news_item.title}")
        return {
            "success": True,
            "news_id": news_item.news_id,
            "message": "模拟推送成功",
            "mock": True
        }
    
    def _filter_pending_news(self, news_items: List[HotNewsItem]) -> List[HotNewsItem]:
        """过滤待推送的新闻"""
        try:
            pending_news = []
            
            for news in news_items:
                # 跳过已推送的新闻
                if news.is_pushed:
                    continue
                
                # 跳过历史信息
                if news.is_historical:
                    continue
                
                # 检查重要程度
                if not self._meets_importance_requirement(news):
                    continue
                
                # 检查是否已在推送历史中
                if self._is_in_push_history(news):
                    continue
                
                pending_news.append(news)
            
            # 按重要程度和热度排序
            pending_news.sort(key=lambda x: (
                self._get_importance_score(x.importance_level),
                x.heat_score or 0
            ), reverse=True)
            
            return pending_news
            
        except Exception as e:
            logger.error(f"过滤待推送新闻失败: {str(e)}")
            return news_items
    
    def _meets_importance_requirement(self, news_item: HotNewsItem) -> bool:
        """检查是否满足重要程度要求"""
        importance_levels = {"low": 1, "medium": 2, "high": 3}
        min_level = importance_levels.get(settings.hot_news_min_importance, 2)
        news_level = importance_levels.get(news_item.importance_level, 2)
        return news_level >= min_level
    
    def _get_importance_score(self, importance_level: str) -> int:
        """获取重要程度评分"""
        scores = {"low": 1, "medium": 2, "high": 3}
        return scores.get(importance_level, 2)
    
    def _can_push_now(self) -> bool:
        """检查是否可以推送"""
        if not self._last_push_time:
            return True
        
        time_diff = (datetime.now() - self._last_push_time).total_seconds()
        return time_diff >= settings.hot_news_push_interval
    
    def _is_in_push_history(self, news_item: HotNewsItem) -> bool:
        """检查是否已在推送历史中"""
        return news_item.news_id in self._push_history
    
    def _record_push_history(self, news_item: HotNewsItem, push_result: Dict[str, Any]):
        """记录推送历史"""
        try:
            self._push_history[news_item.news_id] = {
                "news_id": news_item.news_id,
                "title": news_item.title,
                "push_time": datetime.now().isoformat(),
                "result": push_result
            }
            
            # 限制历史记录数量
            if len(self._push_history) > 1000:
                # 删除最旧的记录
                oldest_keys = list(self._push_history.keys())[:100]
                for key in oldest_keys:
                    del self._push_history[key]
            
        except Exception as e:
            logger.error(f"记录推送历史失败: {str(e)}")
    
    def _cache_push_results(self, push_results: List[Dict[str, Any]]):
        """缓存推送结果"""
        try:
            cache_key = f"push_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            cache_manager.set(cache_key, push_results, expire_seconds=86400)  # 缓存24小时
            
        except Exception as e:
            logger.error(f"缓存推送结果失败: {str(e)}")
    
    def get_push_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取推送历史
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            推送历史记录列表
        """
        try:
            history_items = list(self._push_history.values())
            # 按推送时间倒序排列
            history_items.sort(key=lambda x: x.get("push_time", ""), reverse=True)
            
            return history_items[:limit]
            
        except Exception as e:
            logger.error(f"获取推送历史失败: {str(e)}")
            return []
    
    def get_push_stats(self) -> Dict[str, Any]:
        """
        获取推送统计信息
        
        Returns:
            推送统计信息
        """
        try:
            total_pushes = len(self._push_history)
            
            # 统计今日推送
            today = datetime.now().date()
            today_pushes = 0
            
            for record in self._push_history.values():
                try:
                    push_time = datetime.fromisoformat(record["push_time"])
                    if push_time.date() == today:
                        today_pushes += 1
                except:
                    continue
            
            return {
                "total_pushes": total_pushes,
                "today_pushes": today_pushes,
                "last_push_time": self._last_push_time.isoformat() if self._last_push_time else None,
                "push_enabled": settings.hot_news_push_enabled,
                "push_interval": settings.hot_news_push_interval,
                "batch_size": settings.hot_news_push_batch_size,
                "min_importance": settings.hot_news_min_importance
            }
            
        except Exception as e:
            logger.error(f"获取推送统计失败: {str(e)}")
            return {}
