"""
数据源管理API模块

提供用于动态添加、删除、更新数据源的API接口。
支持RESTful风格的数据源管理操作。
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from loguru import logger

from .models import DataSourceConfig, DataSourceStatus, UnifiedNewsData
from .data_source_registry import data_source_registry
from .hot_news_collector import HotNewsCollector
from .predefined_adapters import get_predefined_sources, create_predefined_source_configs


class DataSourceAPI:
    """数据源管理API类"""
    
    def __init__(self):
        """初始化数据源管理API"""
        self.registry = data_source_registry
        self.collector = HotNewsCollector(use_new_system=True)
        
    def create_source(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建新的数据源
        
        Args:
            source_data: 数据源配置数据
            
        Returns:
            操作结果
        """
        try:
            # 验证必填字段
            required_fields = ['source_id', 'name', 'source_type', 'adapter_class']
            for field in required_fields:
                if field not in source_data:
                    return {
                        "success": False,
                        "error": f"缺少必填字段: {field}"
                    }
            
            # 创建配置对象
            config = DataSourceConfig(**source_data)
            
            # 注册数据源
            success = self.registry.register_source(config)
            
            if success:
                return {
                    "success": True,
                    "message": f"数据源 {config.source_id} 创建成功",
                    "source_id": config.source_id
                }
            else:
                return {
                    "success": False,
                    "error": "数据源创建失败"
                }
                
        except Exception as e:
            logger.error(f"创建数据源失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_source(self, source_id: str) -> Dict[str, Any]:
        """
        获取数据源信息
        
        Args:
            source_id: 数据源ID
            
        Returns:
            数据源信息
        """
        try:
            config = self.registry.get_source(source_id)
            if not config:
                return {
                    "success": False,
                    "error": f"数据源 {source_id} 不存在"
                }
            
            # 获取状态信息
            status = self.registry.get_source_status(source_id)
            
            return {
                "success": True,
                "data": {
                    "config": config.dict(),
                    "status": status.dict() if status else None
                }
            }
            
        except Exception as e:
            logger.error(f"获取数据源信息失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def update_source(self, source_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新数据源配置
        
        Args:
            source_id: 数据源ID
            updates: 更新的配置项
            
        Returns:
            操作结果
        """
        try:
            success = self.registry.update_source(source_id, updates)
            
            if success:
                return {
                    "success": True,
                    "message": f"数据源 {source_id} 更新成功"
                }
            else:
                return {
                    "success": False,
                    "error": f"数据源 {source_id} 更新失败"
                }
                
        except Exception as e:
            logger.error(f"更新数据源失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def delete_source(self, source_id: str) -> Dict[str, Any]:
        """
        删除数据源
        
        Args:
            source_id: 数据源ID
            
        Returns:
            操作结果
        """
        try:
            success = self.registry.unregister_source(source_id)
            
            if success:
                return {
                    "success": True,
                    "message": f"数据源 {source_id} 删除成功"
                }
            else:
                return {
                    "success": False,
                    "error": f"数据源 {source_id} 删除失败"
                }
                
        except Exception as e:
            logger.error(f"删除数据源失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def list_sources(self, enabled_only: bool = False) -> Dict[str, Any]:
        """
        列出所有数据源
        
        Args:
            enabled_only: 是否只返回启用的数据源
            
        Returns:
            数据源列表
        """
        try:
            sources = self.registry.list_sources(enabled_only=enabled_only)
            
            # 获取每个数据源的状态
            sources_data = []
            for source in sources:
                status = self.registry.get_source_status(source.source_id)
                sources_data.append({
                    "config": source.dict(),
                    "status": status.dict() if status else None
                })
            
            return {
                "success": True,
                "data": sources_data,
                "total": len(sources_data)
            }
            
        except Exception as e:
            logger.error(f"列出数据源失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def test_source(self, source_id: str) -> Dict[str, Any]:
        """
        测试数据源连接
        
        Args:
            source_id: 数据源ID
            
        Returns:
            测试结果
        """
        try:
            result = self.registry.test_source(source_id)
            return result
            
        except Exception as e:
            logger.error(f"测试数据源失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def enable_source(self, source_id: str) -> Dict[str, Any]:
        """
        启用数据源
        
        Args:
            source_id: 数据源ID
            
        Returns:
            操作结果
        """
        return self.update_source(source_id, {"enabled": True})
    
    def disable_source(self, source_id: str) -> Dict[str, Any]:
        """
        禁用数据源
        
        Args:
            source_id: 数据源ID
            
        Returns:
            操作结果
        """
        return self.update_source(source_id, {"enabled": False})
    
    def collect_from_source(self, source_id: str) -> Dict[str, Any]:
        """
        从指定数据源收集数据
        
        Args:
            source_id: 数据源ID
            
        Returns:
            收集结果
        """
        try:
            adapter = self.registry.get_adapter(source_id)
            if not adapter:
                return {
                    "success": False,
                    "error": f"数据源 {source_id} 的适配器不存在"
                }
            
            # 获取数据
            start_time = datetime.now()
            data = adapter.fetch_with_retry()
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            return {
                "success": True,
                "data": [item.dict() for item in data],
                "count": len(data),
                "processing_time": processing_time,
                "status": adapter.get_status().dict()
            }
            
        except Exception as e:
            logger.error(f"从数据源收集数据失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def collect_all_sources(self, force_refresh: bool = False) -> Dict[str, Any]:
        """
        从所有启用的数据源收集数据
        
        Args:
            force_refresh: 是否强制刷新
            
        Returns:
            收集结果
        """
        try:
            start_time = datetime.now()
            data = self.collector.collect_unified_news(force_refresh=force_refresh)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            return {
                "success": True,
                "data": [item.dict() for item in data],
                "count": len(data),
                "processing_time": processing_time
            }
            
        except Exception as e:
            logger.error(f"收集所有数据源失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_predefined_sources(self) -> Dict[str, Any]:
        """
        获取预定义数据源配置
        
        Returns:
            预定义数据源列表
        """
        try:
            sources = get_predefined_sources()
            return {
                "success": True,
                "data": sources,
                "total": len(sources)
            }
            
        except Exception as e:
            logger.error(f"获取预定义数据源失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def install_predefined_source(self, source_id: str) -> Dict[str, Any]:
        """
        安装预定义数据源
        
        Args:
            source_id: 预定义数据源ID
            
        Returns:
            操作结果
        """
        try:
            predefined_sources = get_predefined_sources()
            source_data = None
            
            for source in predefined_sources:
                if source['source_id'] == source_id:
                    source_data = source
                    break
            
            if not source_data:
                return {
                    "success": False,
                    "error": f"预定义数据源 {source_id} 不存在"
                }
            
            # 创建数据源
            return self.create_source(source_data)
            
        except Exception as e:
            logger.error(f"安装预定义数据源失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取数据源统计信息
        
        Returns:
            统计信息
        """
        try:
            sources = self.registry.list_sources()
            
            total_sources = len(sources)
            enabled_sources = len([s for s in sources if s.enabled])
            disabled_sources = total_sources - enabled_sources
            
            # 统计各类型数据源
            type_stats = {}
            for source in sources:
                source_type = source.source_type
                type_stats[source_type] = type_stats.get(source_type, 0) + 1
            
            # 统计状态
            status_stats = {"active": 0, "inactive": 0, "error": 0}
            for source in sources:
                status = self.registry.get_source_status(source.source_id)
                if status:
                    status_key = status.status
                    if status_key in status_stats:
                        status_stats[status_key] += 1
            
            return {
                "success": True,
                "data": {
                    "total_sources": total_sources,
                    "enabled_sources": enabled_sources,
                    "disabled_sources": disabled_sources,
                    "type_distribution": type_stats,
                    "status_distribution": status_stats
                }
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }


# 全局API实例
data_source_api = DataSourceAPI()
