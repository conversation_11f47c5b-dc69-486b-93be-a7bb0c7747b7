"""
预定义数据源适配器模块

为常见的新闻源和数据源提供预配置的适配器实现。
包括主流财经媒体、新闻网站、社交媒体等数据源的专用适配器。
"""

from datetime import datetime
from typing import List, Dict, Any
from loguru import logger

from .data_source_adapters import APIAdapter, RSSAdapter, WebAdapter
from .models import UnifiedNewsData, DataSourceConfig
from .utils import safe_json_parse


class TencentFinanceAdapter(APIAdapter):
    """腾讯财经API适配器"""
    
    def fetch_data(self) -> List[UnifiedNewsData]:
        """从腾讯财经API获取数据"""
        url = "https://web.ifzq.gtimg.cn/appstock/app/news/get"
        params = {
            'type': 'finance',
            'count': 50,
            'page': 1
        }
        
        response = self._session.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        return self._parse_tencent_response(data)
    
    def _parse_tencent_response(self, data: Dict[str, Any]) -> List[UnifiedNewsData]:
        """解析腾讯财经API响应"""
        news_list = []
        current_time = datetime.now()
        
        items = data.get('data', {}).get('list', [])
        
        for item in items:
            try:
                title = item.get('title', '').strip()
                if not title:
                    continue
                
                news_id = self.generate_news_id(title, item.get('url', ''))
                
                # 解析时间
                publish_time = None
                if item.get('time'):
                    publish_time = datetime.fromtimestamp(int(item['time']))
                
                news_data = UnifiedNewsData(
                    id=news_id,
                    title=title,
                    content=item.get('digest', ''),
                    source_id=self.config.source_id,
                    source_name=self.config.name,
                    original_url=item.get('url'),
                    publish_time=publish_time,
                    fetch_time=current_time,
                    category='财经',
                    keywords=self.extract_keywords(title),
                    extra_data={'tencent_item': item}
                )
                
                news_list.append(news_data)
                
            except Exception as e:
                logger.warning(f"解析腾讯财经条目失败: {str(e)}")
                continue
        
        return news_list


class XueqiuAdapter(APIAdapter):
    """雪球财经API适配器"""
    
    def fetch_data(self) -> List[UnifiedNewsData]:
        """从雪球API获取数据"""
        url = "https://xueqiu.com/statuses/hot/listV2.json"
        params = {
            'since_id': -1,
            'max_id': -1,
            'size': 50
        }
        
        # 雪球需要特殊的请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://xueqiu.com/'
        }
        
        response = self._session.get(url, params=params, headers=headers)
        response.raise_for_status()
        
        data = response.json()
        return self._parse_xueqiu_response(data)
    
    def _parse_xueqiu_response(self, data: Dict[str, Any]) -> List[UnifiedNewsData]:
        """解析雪球API响应"""
        news_list = []
        current_time = datetime.now()
        
        items = data.get('list', [])
        
        for item in items:
            try:
                title = item.get('title', '').strip()
                if not title:
                    continue
                
                news_id = self.generate_news_id(title, str(item.get('id', '')))
                
                # 解析时间
                publish_time = None
                if item.get('created_at'):
                    publish_time = datetime.fromtimestamp(int(item['created_at']) / 1000)
                
                news_data = UnifiedNewsData(
                    id=news_id,
                    title=title,
                    content=item.get('description', ''),
                    source_id=self.config.source_id,
                    source_name=self.config.name,
                    original_url=f"https://xueqiu.com/{item.get('id', '')}",
                    publish_time=publish_time,
                    fetch_time=current_time,
                    category='股市',
                    view_count=item.get('view_count'),
                    comment_count=item.get('reply_count'),
                    like_count=item.get('like_count'),
                    keywords=self.extract_keywords(title),
                    extra_data={'xueqiu_item': item}
                )
                
                news_list.append(news_data)
                
            except Exception as e:
                logger.warning(f"解析雪球条目失败: {str(e)}")
                continue
        
        return news_list


class BaiduHotAdapter(APIAdapter):
    """百度热搜API适配器"""
    
    def fetch_data(self) -> List[UnifiedNewsData]:
        """从百度热搜API获取数据"""
        url = "https://top.baidu.com/api/board"
        params = {
            'platform': 'wise',
            'tab': 'realtime'
        }
        
        response = self._session.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        return self._parse_baidu_response(data)
    
    def _parse_baidu_response(self, data: Dict[str, Any]) -> List[UnifiedNewsData]:
        """解析百度热搜API响应"""
        news_list = []
        current_time = datetime.now()
        
        items = data.get('data', {}).get('cards', [])
        
        for card in items:
            content_list = card.get('content', [])
            for item in content_list:
                try:
                    title = item.get('word', '').strip()
                    if not title:
                        continue
                    
                    news_id = self.generate_news_id(title, item.get('url', ''))
                    
                    news_data = UnifiedNewsData(
                        id=news_id,
                        title=title,
                        content=item.get('desc', ''),
                        source_id=self.config.source_id,
                        source_name=self.config.name,
                        original_url=item.get('url'),
                        publish_time=current_time,  # 百度热搜没有具体时间
                        fetch_time=current_time,
                        category='热搜',
                        view_count=item.get('hotScore'),
                        keywords=self.extract_keywords(title),
                        extra_data={'baidu_item': item}
                    )
                    
                    news_list.append(news_data)
                    
                except Exception as e:
                    logger.warning(f"解析百度热搜条目失败: {str(e)}")
                    continue
        
        return news_list


class WeiboHotAdapter(APIAdapter):
    """微博热搜API适配器"""
    
    def fetch_data(self) -> List[UnifiedNewsData]:
        """从微博热搜API获取数据"""
        url = "https://weibo.com/ajax/side/hotSearch"
        
        response = self._session.get(url)
        response.raise_for_status()

        # 使用安全JSON解析
        data = safe_json_parse(response, "微博热搜API")
        if data is None:
            return []

        return self._parse_weibo_response(data)
    
    def _parse_weibo_response(self, data: Dict[str, Any]) -> List[UnifiedNewsData]:
        """解析微博热搜API响应"""
        news_list = []
        current_time = datetime.now()
        
        items = data.get('data', {}).get('realtime', [])
        
        for item in items:
            try:
                title = item.get('word_scheme', item.get('word', '')).strip()
                if not title:
                    continue
                
                news_id = self.generate_news_id(title, '')
                
                news_data = UnifiedNewsData(
                    id=news_id,
                    title=title,
                    content=item.get('note', ''),
                    source_id=self.config.source_id,
                    source_name=self.config.name,
                    original_url=f"https://s.weibo.com/weibo?q={title}",
                    publish_time=current_time,
                    fetch_time=current_time,
                    category='微博热搜',
                    view_count=item.get('num'),
                    keywords=self.extract_keywords(title),
                    extra_data={'weibo_item': item}
                )
                
                news_list.append(news_data)
                
            except Exception as e:
                logger.warning(f"解析微博热搜条目失败: {str(e)}")
                continue
        
        return news_list


class JinrongjieBaiduAdapter(WebAdapter):
    """金融界百度新闻Web适配器"""
    
    def _parse_html_content(self, soup) -> List[UnifiedNewsData]:
        """解析金融界新闻页面"""
        news_list = []
        current_time = datetime.now()
        
        # 查找新闻列表
        news_items = soup.select('.news-list li, .list-item')
        
        for item in news_items:
            try:
                # 提取标题和链接
                title_elem = item.select_one('a')
                if not title_elem:
                    continue
                
                title = title_elem.get_text(strip=True)
                url = title_elem.get('href', '')
                
                if url.startswith('/'):
                    url = f"http://finance.jrj.com.cn{url}"
                
                news_id = self.generate_news_id(title, url)
                
                # 提取时间
                time_elem = item.select_one('.time, .date')
                publish_time = None
                if time_elem:
                    time_text = time_elem.get_text(strip=True)
                    publish_time = self.parse_datetime(time_text)
                
                news_data = UnifiedNewsData(
                    id=news_id,
                    title=title,
                    source_id=self.config.source_id,
                    source_name=self.config.name,
                    original_url=url,
                    publish_time=publish_time,
                    fetch_time=current_time,
                    category='财经',
                    keywords=self.extract_keywords(title)
                )
                
                news_list.append(news_data)
                
            except Exception as e:
                logger.warning(f"解析金融界条目失败: {str(e)}")
                continue
        
        return news_list


# 预定义适配器配置
PREDEFINED_SOURCES = [
    {
        "source_id": "tencent_finance",
        "name": "腾讯财经",
        "source_type": "api",
        "adapter_class": "TencentFinanceAdapter",
        "config": {},
        "enabled": True,
        "priority": 1,
        "fetch_interval": 300
    },
    {
        "source_id": "xueqiu_hot",
        "name": "雪球热门",
        "source_type": "api", 
        "adapter_class": "XueqiuAdapter",
        "config": {},
        "enabled": True,
        "priority": 2,
        "fetch_interval": 600
    },
    {
        "source_id": "baidu_hot",
        "name": "百度热搜",
        "source_type": "api",
        "adapter_class": "BaiduHotAdapter", 
        "config": {},
        "enabled": True,
        "priority": 3,
        "fetch_interval": 900
    },
    {
        "source_id": "weibo_hot",
        "name": "微博热搜",
        "source_type": "api",
        "adapter_class": "WeiboHotAdapter",
        "config": {},
        "enabled": True,
        "priority": 4,
        "fetch_interval": 900
    },
    {
        "source_id": "jrj_news",
        "name": "金融界新闻",
        "source_type": "web",
        "adapter_class": "JinrongjieBaiduAdapter",
        "config": {
            "url": "http://finance.jrj.com.cn/list/news.shtml",
            "selectors": {
                "container": ".news-list li",
                "title": "a",
                "link": "a",
                "time": ".time"
            }
        },
        "enabled": True,
        "priority": 5,
        "fetch_interval": 1800
    }
]


def get_predefined_sources() -> List[Dict[str, Any]]:
    """
    获取预定义数据源配置
    
    Returns:
        预定义数据源配置列表
    """
    return PREDEFINED_SOURCES.copy()


def create_predefined_source_configs() -> List[DataSourceConfig]:
    """
    创建预定义数据源配置对象
    
    Returns:
        数据源配置对象列表
    """
    configs = []
    for source_data in PREDEFINED_SOURCES:
        try:
            config = DataSourceConfig(**source_data)
            configs.append(config)
        except Exception as e:
            logger.warning(f"创建预定义配置失败: {source_data['source_id']}, {str(e)}")
    
    return configs
