#!/usr/bin/env python3
"""
调试搜索结果解析逻辑
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis.models import HotNewsItem
from financial_analysis.hot_news_analyzer import HotNewsAnalyzer
from loguru import logger

# 设置日志级别为DEBUG
logger.remove()
logger.add(sys.stderr, level="DEBUG")

def test_parse_logic():
    """测试解析逻辑"""
    
    # 创建测试数据
    current_time = datetime.now()
    news_items = [
        HotNewsItem(
            news_id="test_1",
            title="最新财经新闻",
            content="今日股市表现良好",
            source="财经日报",
            channel_id="finance",
            url="https://example.com/1",
            publish_time=current_time - timedelta(hours=1),
            fetch_time=current_time
        ),
        HotNewsItem(
            news_id="test_2",
            title="历史新闻事件",
            content="这是一个历史事件",
            source="历史周刊",
            channel_id="history",
            url="https://example.com/2",
            publish_time=current_time - timedelta(days=5),
            fetch_time=current_time
        )
    ]
    
    # 模拟搜索结果
    search_result = """
1. 新闻编号: 1
2. 是否为历史消息: 否
3. 最初发布时间: 2024-01-20 10:00:00
4. 判断依据: 最近发布的新闻

1. 新闻编号: 2
2. 是否为历史消息: 是
3. 最初发布时间: 2024-01-15 14:00:00
4. 判断依据: 历史新闻
"""
    
    logger.info("开始测试解析逻辑")
    logger.info(f"原始新闻数量: {len(news_items)}")
    logger.info("搜索结果:")
    logger.info(search_result)
    
    # 创建分析器并解析
    analyzer = HotNewsAnalyzer()
    result = analyzer._parse_search_result(news_items, search_result)
    
    logger.info(f"解析结果数量: {len(result)}")
    for i, news in enumerate(result):
        logger.info(f"新闻 {i+1}: {news.title}")
        logger.info(f"  是否历史: {news.is_historical}")
        logger.info(f"  发布时间: {news.publish_time}")
    
    return result

def test_line_by_line_parsing():
    """逐行测试解析逻辑"""
    
    search_result = """
1. 新闻编号: 1
2. 是否为历史消息: 否
3. 最初发布时间: 2024-01-20 10:00:00
4. 判断依据: 最近发布的新闻

1. 新闻编号: 2
2. 是否为历史消息: 是
3. 最初发布时间: 2024-01-15 14:00:00
4. 判断依据: 历史新闻
"""
    
    logger.info("=== 逐行解析测试 ===")
    
    lines = search_result.split('\n')
    current_news_index = -1
    is_historical = False
    
    for line_num, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
            
        logger.info(f"第{line_num}行: '{line}'")
        
        # 检查是否是新的新闻项开始
        if line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')) or \
           '新闻编号:' in line:
            logger.info(f"  -> 检测到新闻项开始")
            
            # 提取新闻编号
            try:
                if '新闻编号:' in line:
                    current_news_index = int(line.split('新闻编号:')[1].strip()) - 1
                else:
                    current_news_index = int(line.split('.')[0]) - 1
                logger.info(f"  -> 新闻索引: {current_news_index}")
            except Exception as e:
                logger.error(f"  -> 提取新闻编号失败: {e}")
                current_news_index = -1
            
            # 重置状态
            is_historical = False
        
        # 解析是否为历史消息
        elif '是否为历史消息:' in line or '历史消息:' in line:
            result_text = line.split(':')[1].strip().lower()
            is_historical = '是' in result_text or 'true' in result_text or '历史' in result_text
            logger.info(f"  -> 历史消息判断: '{result_text}' -> {is_historical}")
        
        # 解析最初发布时间
        elif '最初发布时间:' in line or '发布时间:' in line:
            time_text = line.split(':')[1].strip()
            logger.info(f"  -> 发布时间: '{time_text}'")

if __name__ == "__main__":
    test_line_by_line_parsing()
    print("\n" + "="*50 + "\n")
    test_parse_logic()
