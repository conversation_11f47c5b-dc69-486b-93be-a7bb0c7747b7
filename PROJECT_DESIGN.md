# 金融证券分析项目设计

## 项目结构

- 主要使用 python 开发
- 使用 markdown 编写文档

## 项目目标

本项目旨在利用人工智能技术，特别是大语言模型，来辅助进行金融证券分析。通过构建一个基于大语言模型的分析工具，我们可以实现以下目标：

1. **自动化分析**：利用大语言模型自动处理大量的金融数据，包括但不限于股票、债券、期货等证券的数据。
2. **智能化报告生成**：根据分析结果，自动生成详细的分析报告，包括趋势预测、风险评估等。
3. **提高分析效率**：通过自动化和智能化的手段，显著提高金融证券分析的效率，使分析师能够更专注于高附加值的任务。
4. **辅助决策支持**：为投资者和分析师提供基于数据的决策支持，帮助他们做出更明智的投资决策。

## 功能设计

### 股票分析

- 通过获取到输入的股票代码, 支持多个常见的交易所的股票代码
- 通过调用 gemini 大语言模型，通过搜索功能获取到股票近期的消息
- 通过调用金融数据接口获取股票近期的相关参数
- 生成分析报告
