# 项目: 金融证券分析

## 一般说明

- 创建新模块或者新的类时需要在 docs 目录下新建说明文档, 使用 markdown 格式
- 关键的代码和配置必须有详细的中文说明
- 接口和暴露的方法需要使用方法级别的注释说明功能, 入参出参也需要使用中文注释
- 输出日志采用中文格式

## 工作流程

- 接收到任务时, 分析任务的复杂度, 复杂的任务请先做好规划后再实施, 最后验证
- 编写工具类时, 需要添加完整的单元测试
- 编写测试脚本时, 需要调用后端接口使用 Mock 模式
- 功能实现后总结修改的内容并生成中文描述的 git commit 并提交

## 大模型接口

### 生成文本接口说明

- POST: https://wm.atjog.com/api/w/my-workspace/jobs/run/p/f/gemini/text_generation
- 参数:
  - prompt: 提示词, 必填
  - search: 是否使用搜索工具, 可选, 默认为 false

### 生成结构化文本接口说明

- POST: https://wm.atjog.com/api/w/my-workspace/jobs/run/p/f/gemini/js_structured_output
- 参数:
  - prompt: 提示词, 必填
  - system_instruction: 系统提示词, 可选
  - responseSchema: 结构化输出格式, 必填

## 配置说明

### 环境变量配置

在 `.env` 文件中配置以下变量：

```env
# 大模型配置（使用Windmill生成文本接口）
GEMINI_MODEL=gemini-pro

# Windmill配置
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your_windmill_token
WINDMILL_WORKSPACE=my-workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=js_structured_output
```

### 获取 Windmill Token

1. 访问 Windmill 控制台
2. 创建新的 API Token
3. 将 Token 添加到 `.env` 文件中

## 迁移说明

本项目已从直接使用 Google Gemini API 迁移到使用 Windmill 的生成文本接口：

### 迁移的优势
- 统一的接口管理
- 更好的错误处理和重试机制
- 支持搜索功能增强
- 更灵活的配置管理

### 已迁移的功能
- 新闻分析和情感分析
- 股票分析报告生成
- 热点信息智能分析
- 历史消息检测（使用搜索功能）

### 配置变更
- 移除了直接的 Google Gemini API 密钥配置
- 新增了 Windmill 相关配置
- 保留了 `GEMINI_MODEL` 用于标识模型类型

