#!/usr/bin/env python3
"""
真实金融数据配置脚本

自动配置项目以获取真实的金融数据，包括依赖安装、配置检查和数据源验证。
"""

import os
import sys
import subprocess
from datetime import datetime

def install_dependencies():
    """安装必要的依赖包"""
    print("=" * 60)
    print("安装必要依赖包")
    print("=" * 60)
    
    dependencies = [
        "akshare>=1.17.0",      # 中国股票数据
        "yfinance>=0.2.0",      # 国际股票数据
        "aiohttp>=3.8.0",       # 异步HTTP请求
        "requests>=2.28.0",     # HTTP请求
        "pandas>=1.5.0",        # 数据处理
        "numpy>=1.23.0",        # 数值计算
        "loguru>=0.6.0",        # 日志管理
        "pydantic>=2.0.0",      # 数据验证
        "pydantic-settings>=2.0.0",  # 配置管理
    ]
    
    for dep in dependencies:
        print(f"\n安装 {dep}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], capture_output=True, text=True, check=True)
            print(f"✓ {dep} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ {dep} 安装失败: {e}")
            print(f"错误输出: {e.stderr}")

def check_data_sources():
    """检查数据源可用性"""
    print("\n" + "=" * 60)
    print("检查数据源可用性")
    print("=" * 60)
    
    # 检查akshare
    print("\n1. 检查akshare（中国股票数据）...")
    try:
        import akshare as ak
        # 测试获取股票信息
        stock_info = ak.stock_individual_info_em(symbol="000001")
        if not stock_info.empty:
            print("✓ akshare 可用，中国股票数据正常")
        else:
            print("⚠️ akshare 可用，但数据获取异常")
    except ImportError:
        print("✗ akshare 未安装")
    except Exception as e:
        print(f"⚠️ akshare 测试失败: {str(e)}")
    
    # 检查yfinance
    print("\n2. 检查yfinance（国际股票数据）...")
    try:
        import yfinance as yf
        # 测试获取股票信息
        ticker = yf.Ticker("AAPL")
        info = ticker.info
        if info and 'symbol' in info:
            print("✓ yfinance 可用，国际股票数据正常")
        else:
            print("⚠️ yfinance 可用，但数据获取异常")
    except ImportError:
        print("✗ yfinance 未安装")
    except Exception as e:
        print(f"⚠️ yfinance 测试失败: {str(e)}")

def create_optimized_config():
    """创建优化的配置文件"""
    print("\n" + "=" * 60)
    print("创建优化配置")
    print("=" * 60)
    
    config_content = """# 金融分析项目配置文件 - 真实数据版本
# 生成时间: {timestamp}

# 大模型配置
GEMINI_MODEL=gemini-2.5-flash

# Windmill配置（用于AI分析和新闻搜索）
# 注意：需要有效的token和权限才能获取真实新闻数据
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your_windmill_token_here
WINDMILL_WORKSPACE=my-workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=js_structured_output

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/financial_analysis.log

# 数据源配置
DEFAULT_EXCHANGE=SSE
DATA_CACHE_DURATION=300  # 数据缓存时间（秒）

# 分析配置
ANALYSIS_DAYS=30         # 分析的历史天数
NEWS_SEARCH_DAYS=7       # 新闻搜索的天数范围

# 热点信息配置
HOT_NEWS_ENABLED=true
HOT_NEWS_FETCH_INTERVAL=300
HOT_NEWS_CACHE_DURATION=1800
HOT_NEWS_MAX_ITEMS=100
HOT_NEWS_HISTORY_CHECK_DAYS=3

# 热点信息推送配置
HOT_NEWS_PUSH_ENABLED=true
HOT_NEWS_PUSH_INTERVAL=600
HOT_NEWS_PUSH_BATCH_SIZE=10
HOT_NEWS_MIN_IMPORTANCE=medium

# 数据质量配置
ENABLE_DATA_VALIDATION=true     # 启用数据验证
FALLBACK_TO_MOCK=true          # 数据获取失败时是否降级到模拟数据
MAX_RETRY_ATTEMPTS=3           # 最大重试次数
RETRY_DELAY=1                  # 重试延迟（秒）

# API限制配置
AKSHARE_RATE_LIMIT=100         # akshare每分钟最大请求数
YFINANCE_RATE_LIMIT=200        # yfinance每分钟最大请求数
""".format(timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 备份现有配置
    if os.path.exists('.env'):
        backup_name = f'.env.backup.{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        os.rename('.env', backup_name)
        print(f"✓ 现有配置已备份为: {backup_name}")
    
    # 写入新配置
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(config_content)
    print("✓ 新配置文件已创建: .env")
    
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    print("✓ 日志目录已创建: logs/")

def test_real_data():
    """测试真实数据获取"""
    print("\n" + "=" * 60)
    print("测试真实数据获取")
    print("=" * 60)
    
    try:
        # 添加项目路径
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from financial_analysis import StockDataProvider
        
        provider = StockDataProvider()
        
        # 测试中国A股
        print("\n1. 测试中国A股数据...")
        stock_info = provider.get_stock_info("000001", "SZSE")
        if stock_info:
            print(f"✓ 股票信息: {stock_info.name} ({stock_info.symbol})")
            
            prices = provider.get_stock_prices("000001", days=3, exchange="SZSE")
            if prices:
                latest_price = prices[-1].close_price
                print(f"✓ 最新价格: {latest_price:.2f} CNY")
                
                # 判断是否为真实数据
                if latest_price > 5 and latest_price < 100:  # 合理的价格范围
                    print("✓ 数据验证通过，疑似真实市场数据")
                else:
                    print("⚠️ 数据异常，请检查数据源")
            else:
                print("✗ 价格数据获取失败")
        else:
            print("✗ 股票信息获取失败")
        
        # 测试美股
        print("\n2. 测试美股数据...")
        stock_info = provider.get_stock_info("AAPL", "NASDAQ")
        if stock_info:
            print(f"✓ 股票信息: {stock_info.name} ({stock_info.symbol})")
            
            prices = provider.get_stock_prices("AAPL", days=3, exchange="NASDAQ")
            if prices:
                latest_price = prices[-1].close_price
                print(f"✓ 最新价格: {latest_price:.2f} USD")
                print("✓ 国际股票数据正常")
            else:
                print("✗ 价格数据获取失败")
        else:
            print("✗ 股票信息获取失败")
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")

def print_next_steps():
    """打印后续步骤"""
    print("\n" + "=" * 60)
    print("后续配置步骤")
    print("=" * 60)
    
    print("""
📋 完成真实数据配置的后续步骤：

1. 🔑 配置Windmill Token（获取真实新闻和AI分析）：
   - 编辑 .env 文件
   - 将 WINDMILL_TOKEN 替换为有效的token
   - 确保token具有 run:script/f/gemini/js_structured_output 权限

2. 🧪 运行完整测试：
   python test_data_sources.py

3. 🚀 运行演示：
   python demo.py

4. 📊 使用API：
   from financial_analysis import AnalysisEngine
   engine = AnalysisEngine()
   report = engine.generate_analysis_report("000001", "SZSE")

⚠️ 重要提醒：
- 股票数据现在应该是真实的
- 新闻和AI分析需要有效的Windmill配置
- 请遵守各数据源的使用条款和频率限制
- 投资有风险，分析结果仅供参考

📚 更多信息：
- 故障排除: docs/troubleshooting.md
- API文档: docs/api.md
- 使用示例: docs/examples.md
""")

def main():
    """主函数"""
    print("🚀 金融分析项目 - 真实数据配置向导")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 安装依赖
        install_dependencies()
        
        # 2. 检查数据源
        check_data_sources()
        
        # 3. 创建配置
        create_optimized_config()
        
        # 4. 测试数据
        test_real_data()
        
        # 5. 打印后续步骤
        print_next_steps()
        
        print("\n✅ 真实数据配置完成！")
        
    except KeyboardInterrupt:
        print("\n\n❌ 配置被用户中断")
    except Exception as e:
        print(f"\n\n❌ 配置过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
