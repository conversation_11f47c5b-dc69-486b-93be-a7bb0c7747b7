#!/usr/bin/env python3
"""
历史消息检测功能使用示例

这个脚本展示了如何使用新实现的历史消息检测功能。
"""

import sys
import os
from datetime import datetime, timedelta
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis.models import HotNewsItem
from financial_analysis.hot_news_manager import hot_news_manager
from financial_analysis.hot_news_analyzer import HotNewsAnalyzer
from loguru import logger


def example_1_basic_usage():
    """示例1: 基本使用方法"""
    logger.info("=== 示例1: 基本使用方法 ===")
    
    # 创建一些示例新闻
    current_time = datetime.now()
    news_items = [
        HotNewsItem(
            news_id="news_1",
            title="最新财经新闻：股市今日大涨",
            content="今日A股市场表现强劲，上证指数上涨2.5%...",
            source="财经日报",
            channel_id="finance_channel",
            url="https://example.com/news/1",
            publish_time=current_time - timedelta(hours=2),
            fetch_time=current_time
        ),
        HotNewsItem(
            news_id="news_2", 
            title="科技巨头发布新产品",
            content="某科技公司今日发布了最新的智能手机产品...",
            source="科技周刊",
            channel_id="tech_channel",
            url="https://example.com/news/2",
            publish_time=current_time - timedelta(days=1),
            fetch_time=current_time
        )
    ]
    
    # 方法1: 使用管理器检查历史消息
    result = hot_news_manager.check_historical_messages_with_search(news_items)
    
    logger.info(f"检查结果: {result['message']}")
    logger.info(f"原始消息数: {result['original_count']}")
    logger.info(f"历史消息数: {result['historical_count']}")
    logger.info(f"最近消息数: {result['recent_count']}")
    
    return result


def example_2_filter_recent_messages():
    """示例2: 直接获取最近消息"""
    logger.info("=== 示例2: 直接获取最近消息 ===")
    
    # 创建包含不同时间的新闻
    current_time = datetime.now()
    mixed_news = [
        HotNewsItem(
            news_id="recent_1",
            title="今日热点：新政策发布",
            content="政府今日发布了新的经济政策...",
            source="政策通",
            channel_id="policy_channel",
            url="https://example.com/recent/1",
            publish_time=current_time - timedelta(hours=1),
            fetch_time=current_time
        ),
        HotNewsItem(
            news_id="old_1",
            title="上周新闻：某公司财报发布",
            content="某公司上周发布了季度财报...",
            source="财报网",
            channel_id="earnings_channel", 
            url="https://example.com/old/1",
            publish_time=current_time - timedelta(days=7),
            fetch_time=current_time
        ),
        HotNewsItem(
            news_id="recent_2",
            title="突发：市场异动",
            content="股市出现异常波动，监管部门回应...",
            source="市场快讯",
            channel_id="market_channel",
            url="https://example.com/recent/2", 
            publish_time=current_time - timedelta(minutes=30),
            fetch_time=current_time
        )
    ]
    
    # 直接获取最近消息
    recent_messages = hot_news_manager.get_recent_messages_only(mixed_news)
    
    logger.info(f"从 {len(mixed_news)} 条消息中筛选出 {len(recent_messages)} 条最近消息:")
    for i, news in enumerate(recent_messages, 1):
        logger.info(f"{i}. {news.title}")
        logger.info(f"   发布时间: {news.publish_time}")
        logger.info(f"   是否历史: {news.is_historical}")
    
    return recent_messages


def example_3_analyzer_direct_usage():
    """示例3: 直接使用分析器"""
    logger.info("=== 示例3: 直接使用分析器 ===")
    
    # 创建分析器实例
    analyzer = HotNewsAnalyzer()
    
    # 创建测试新闻
    current_time = datetime.now()
    test_news = [
        HotNewsItem(
            news_id="test_news_1",
            title="重要公告：央行调整利率",
            content="中央银行宣布调整基准利率...",
            source="央行公告",
            channel_id="central_bank",
            url="https://example.com/announcement/1",
            publish_time=current_time - timedelta(hours=3),
            fetch_time=current_time
        )
    ]
    
    # 使用分析器检查历史消息
    checked_news = analyzer.check_historical_news_with_search(test_news)
    
    logger.info("分析器检查结果:")
    for news in checked_news:
        status = "历史消息" if news.is_historical else "最近消息"
        logger.info(f"- {news.title} -> {status}")
    
    return checked_news


def example_4_integration_with_collection():
    """示例4: 与消息收集流程集成"""
    logger.info("=== 示例4: 与消息收集流程集成 ===")
    
    try:
        # 模拟完整的消息处理流程
        logger.info("开始完整的热点信息处理流程...")
        
        # 这会触发完整的流程：收集 -> 搜索检查历史 -> 分析 -> 过滤 -> 推送
        result = hot_news_manager.collect_and_process_news(force_refresh=True)
        
        logger.info("处理结果:")
        logger.info(f"- 成功: {result['success']}")
        logger.info(f"- 消息: {result['message']}")
        logger.info(f"- 收集数量: {result.get('collected_count', 0)}")
        logger.info(f"- 搜索检查数量: {result.get('search_checked_count', 0)}")
        logger.info(f"- 历史消息数量: {result.get('historical_count', 0)}")
        logger.info(f"- 分析数量: {result.get('analyzed_count', 0)}")
        logger.info(f"- 最终过滤数量: {result.get('filtered_count', 0)}")
        logger.info(f"- 处理时间: {result.get('processing_time', 0)}秒")
        
        return result
        
    except Exception as e:
        logger.error(f"集成测试失败: {str(e)}")
        return {"success": False, "error": str(e)}


def main():
    """主函数 - 运行所有示例"""
    logger.info("开始运行历史消息检测功能示例")
    
    try:
        # 示例1: 基本使用
        example_1_basic_usage()
        print("\n" + "="*50 + "\n")
        
        # 示例2: 过滤最近消息
        example_2_filter_recent_messages()
        print("\n" + "="*50 + "\n")
        
        # 示例3: 直接使用分析器
        example_3_analyzer_direct_usage()
        print("\n" + "="*50 + "\n")
        
        # 示例4: 集成使用
        example_4_integration_with_collection()
        
        logger.info("所有示例运行完成")
        
    except Exception as e:
        logger.error(f"示例运行失败: {str(e)}")


if __name__ == "__main__":
    main()
