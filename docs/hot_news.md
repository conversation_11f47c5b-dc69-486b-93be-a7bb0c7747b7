# 实时热点信息功能说明

## 概述

实时热点信息功能是一个完整的热点信息获取、分析、缓存和推送系统。该功能通过多个渠道获取最新的热点消息，使用Gemini大模型判断消息是否为历史信息，将信息存储在缓存中并设置过期时间，通过Windmill接口推送消息，并提供接口实现通过消息ID获取详细信息。

## 主要特性

- 🔄 **多渠道数据获取**: 支持RSS订阅、API接口、网页爬取等多种数据源
- 🤖 **AI智能分析**: 使用Gemini大模型进行历史信息判断、情感分析和重要程度评估
- 💾 **智能缓存管理**: 支持内存缓存和持久化存储，自动过期清理
- 📤 **消息推送**: 通过Windmill接口将热点信息推送给用户
- 🔍 **详情查询**: 提供根据消息ID获取详细信息的API
- 📊 **统计分析**: 提供丰富的统计信息和数据分析

## 核心模块

### 1. HotNewsCollector - 热点信息收集器

负责从多个渠道收集热点信息。

**主要功能:**
- 支持RSS、API、网页爬取等多种数据源
- 自动去重和数据清洗
- 可配置的获取间隔和优先级

**使用示例:**
```python
from financial_analysis import HotNewsCollector

collector = HotNewsCollector()
news_items = collector.collect_hot_news(force_refresh=True)
print(f"收集到 {len(news_items)} 条热点信息")
```

### 2. HotNewsAnalyzer - 热点信息分析器

使用Gemini大模型对热点信息进行智能分析。

**主要功能:**
- 判断是否为历史信息
- 情感分析（positive/negative/neutral）
- 重要程度评估（high/medium/low）
- 关键词提取和分类

**使用示例:**
```python
from financial_analysis import HotNewsAnalyzer

analyzer = HotNewsAnalyzer()
analyzed_news = analyzer.analyze_news_batch(news_items)

for news in analyzed_news:
    print(f"标题: {news.title}")
    print(f"情感: {news.sentiment}")
    print(f"重要程度: {news.importance_level}")
    print(f"是否历史信息: {news.is_historical}")
```

### 3. HotNewsCacheManager - 缓存管理器

提供高效的缓存存储和管理功能。

**主要功能:**
- 内存缓存和持久化存储
- 自动过期清理
- 缓存统计和监控

**使用示例:**
```python
from financial_analysis import cache_manager

# 缓存新闻列表
cache_manager.cache_news_list(news_items, "latest_news")

# 获取缓存的新闻
cached_news = cache_manager.get_cached_news_list("latest_news")

# 获取缓存统计
stats = cache_manager.get_cache_stats()
print(f"缓存统计: {stats}")
```

### 4. HotNewsPusher - 消息推送器

通过Windmill接口推送热点信息。

**主要功能:**
- 批量推送和单条推送
- 推送状态管理
- 推送历史记录

**使用示例:**
```python
from financial_analysis import HotNewsPusher

pusher = HotNewsPusher()
push_result = pusher.push_news_batch(news_items)

print(f"推送结果: {push_result['message']}")
print(f"成功推送: {push_result['pushed_count']} 条")
```

### 5. HotNewsManager - 热点信息管理器

整合所有功能的统一管理接口。

**主要功能:**
- 自动收集和处理流程
- 新闻查询和搜索
- 统计分析和监控

**使用示例:**
```python
from financial_analysis import hot_news_manager

# 启动自动收集服务
hot_news_manager.start_auto_collection()

# 手动收集和处理
result = hot_news_manager.collect_and_process_news()

# 根据ID获取新闻详情
news_detail = hot_news_manager.get_news_by_id("news_id_123")

# 获取最新新闻
latest_news = hot_news_manager.get_latest_news(limit=10)

# 搜索新闻
search_results = hot_news_manager.search_news("股票市场")

# 获取统计信息
stats = hot_news_manager.get_news_statistics()
```

## 数据模型

### HotNewsItem - 热点信息条目

```python
class HotNewsItem(BaseModel):
    news_id: str                    # 新闻唯一标识
    title: str                      # 新闻标题
    content: Optional[str]          # 新闻内容
    summary: Optional[str]          # 新闻摘要
    source: str                     # 新闻来源
    channel_id: str                 # 来源渠道ID
    url: Optional[str]              # 新闻链接
    publish_time: datetime          # 发布时间
    fetch_time: datetime            # 获取时间
    
    # 热度相关
    heat_score: Optional[float]     # 热度评分
    view_count: Optional[int]       # 浏览量
    comment_count: Optional[int]    # 评论数
    
    # 分类和标签
    category: Optional[str]         # 新闻分类
    tags: List[str]                 # 新闻标签
    keywords: List[str]             # 关键词
    
    # AI分析结果
    is_historical: Optional[bool]   # 是否为历史信息
    sentiment: Optional[str]        # 情感倾向
    importance_level: Optional[str] # 重要程度
    
    # 推送状态
    is_pushed: bool                 # 是否已推送
    push_time: Optional[datetime]   # 推送时间
```

### HotNewsChannel - 数据源渠道

```python
class HotNewsChannel(BaseModel):
    channel_id: str                 # 渠道唯一标识
    name: str                       # 渠道名称
    channel_type: str               # 渠道类型：rss/api/web/social
    url: Optional[str]              # 数据源URL
    api_key: Optional[str]          # API密钥
    enabled: bool                   # 是否启用
    priority: int                   # 优先级
    fetch_interval: int             # 获取间隔（秒）
    last_fetch_time: Optional[datetime] # 最后获取时间
```

## 配置参数

在 `.env` 文件中配置以下参数：

```env
# 热点信息配置
HOT_NEWS_ENABLED=true                    # 是否启用热点信息功能
HOT_NEWS_FETCH_INTERVAL=300              # 热点信息获取间隔（秒）
HOT_NEWS_CACHE_DURATION=1800             # 热点信息缓存时间（秒）
HOT_NEWS_MAX_ITEMS=100                   # 单次获取的最大新闻数量
HOT_NEWS_HISTORY_CHECK_DAYS=3            # 历史信息检查天数

# 热点信息推送配置
HOT_NEWS_PUSH_ENABLED=true               # 是否启用热点信息推送
HOT_NEWS_PUSH_INTERVAL=600               # 推送间隔（秒）
HOT_NEWS_PUSH_BATCH_SIZE=10              # 单次推送的最大数量
HOT_NEWS_MIN_IMPORTANCE=medium           # 推送的最低重要程度

# 热点信息数据源配置
HOT_NEWS_CHANNELS=[]                     # 数据源配置（JSON格式）
```

## 完整使用示例

### 基础使用

```python
from financial_analysis import hot_news_manager

# 1. 启动自动收集服务
hot_news_manager.start_auto_collection()

# 2. 获取最新热点信息
latest_news = hot_news_manager.get_latest_news(limit=20)
for news in latest_news:
    print(f"标题: {news.title}")
    print(f"来源: {news.source}")
    print(f"重要程度: {news.importance_level}")
    print(f"情感倾向: {news.sentiment}")
    print("-" * 50)

# 3. 搜索特定主题的新闻
search_results = hot_news_manager.search_news("人工智能")
print(f"找到 {len(search_results)} 条相关新闻")

# 4. 获取新闻详情
if search_results:
    news_detail = hot_news_manager.get_news_by_id(search_results[0].news_id)
    print(f"详细内容: {news_detail.content}")

# 5. 获取统计信息
stats = hot_news_manager.get_news_statistics()
print(f"总新闻数: {stats['total_news']}")
print(f"分类分布: {stats['category_distribution']}")
```

### 高级使用

```python
from financial_analysis import (
    HotNewsCollector, HotNewsAnalyzer, 
    HotNewsPusher, cache_manager
)

# 1. 自定义收集器配置
collector = HotNewsCollector()
raw_news = collector.collect_hot_news(force_refresh=True)

# 2. 分析新闻
analyzer = HotNewsAnalyzer()
analyzed_news = analyzer.analyze_news_batch(raw_news)

# 3. 过滤历史信息和低重要性新闻
filtered_news = analyzer.filter_historical_news(analyzed_news)
important_news = analyzer.filter_by_importance(filtered_news, "high")

# 4. 缓存处理结果
cache_manager.cache_news_list(important_news, "important_news")

# 5. 推送重要新闻
pusher = HotNewsPusher()
push_result = pusher.push_news_batch(important_news, force_push=True)

print(f"处理完成，推送了 {push_result['pushed_count']} 条重要新闻")
```

### 自定义数据源

```python
import json
from financial_analysis.models import HotNewsChannel

# 定义自定义数据源
custom_channels = [
    {
        "channel_id": "custom_rss",
        "name": "自定义RSS源",
        "channel_type": "rss",
        "url": "https://example.com/custom.xml",
        "enabled": True,
        "priority": 1,
        "fetch_interval": 300
    },
    {
        "channel_id": "custom_api",
        "name": "自定义API",
        "channel_type": "api",
        "url": "https://api.example.com/news",
        "api_key": "your_api_key",
        "enabled": True,
        "priority": 2,
        "fetch_interval": 600,
        "params": {"limit": 50, "category": "tech"}
    }
]

# 在环境变量中设置
import os
os.environ['HOT_NEWS_CHANNELS'] = json.dumps(custom_channels)
```

## API参考

### HotNewsManager API

#### collect_and_process_news(force_refresh=False)
收集并处理热点信息

**参数:**
- `force_refresh` (bool): 是否强制刷新

**返回:**
- `Dict[str, Any]`: 处理结果统计

#### get_news_by_id(news_id)
根据新闻ID获取详细信息

**参数:**
- `news_id` (str): 新闻唯一标识

**返回:**
- `Optional[HotNewsItem]`: 新闻详细信息

#### get_latest_news(limit=20, category=None, importance=None)
获取最新热点信息

**参数:**
- `limit` (int): 返回数量限制
- `category` (str, 可选): 新闻分类过滤
- `importance` (str, 可选): 重要程度过滤

**返回:**
- `List[HotNewsItem]`: 最新热点信息列表

#### search_news(keyword, limit=20)
搜索热点信息

**参数:**
- `keyword` (str): 搜索关键词
- `limit` (int): 返回数量限制

**返回:**
- `List[HotNewsItem]`: 搜索结果列表

#### get_news_statistics()
获取新闻统计信息

**返回:**
- `Dict[str, Any]`: 统计信息

## 注意事项

1. **API配置**: 需要有效的Windmill配置才能使用完整功能
2. **频率限制**: 合理设置获取间隔，避免过于频繁的API调用
3. **存储空间**: 缓存会占用磁盘空间，定期清理过期数据
4. **网络依赖**: 功能依赖网络连接，需要确保网络稳定
5. **数据质量**: 分析结果的质量依赖于大语言模型的能力

## 故障排除

### 常见问题

1. **收集不到新闻**
   - 检查网络连接
   - 验证数据源URL是否有效
   - 查看日志文件获取详细错误信息

2. **分析功能异常**
   - 检查Windmill配置是否正确
   - 验证API密钥是否有效
   - 确认Gemini服务是否可用

3. **推送失败**
   - 检查推送配置
   - 验证Windmill推送接口
   - 查看推送历史记录

4. **缓存问题**
   - 检查磁盘空间
   - 验证缓存目录权限
   - 清理过期缓存数据

### 日志查看

```python
from loguru import logger

# 查看详细日志
logger.add("hot_news.log", level="DEBUG")
```

## 性能优化

1. **合理设置缓存时间**: 根据数据更新频率调整缓存时间
2. **优化获取间隔**: 平衡数据实时性和系统负载
3. **限制数据量**: 设置合理的最大新闻数量
4. **异步处理**: 使用后台线程进行数据处理
5. **数据库存储**: 对于大量数据，考虑使用数据库存储
