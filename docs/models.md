# 数据模型说明

## 概述

`models.py` 模块定义了项目中使用的所有数据模型，包括股票信息、分析报告等。使用pydantic进行数据验证和序列化。

## 主要模型

### StockInfo - 股票基本信息模型

表示股票的基本信息。

**字段：**
- `symbol` (str): 股票代码，必填
- `name` (str): 股票名称，必填
- `exchange` (str): 交易所，必填
- `currency` (str): 货币单位，默认"CNY"
- `sector` (str, 可选): 所属行业
- `market_cap` (float, 可选): 市值

**示例：**
```python
stock_info = StockInfo(
    symbol="000001",
    name="平安银行",
    exchange="SZSE",
    currency="CNY",
    sector="银行业",
    market_cap=150000000000
)
```

### StockPrice - 股票价格数据模型

表示单个交易日的股票价格数据。

**字段：**
- `symbol` (str): 股票代码，必填
- `date` (datetime): 日期，必填
- `open_price` (float): 开盘价，必填
- `high_price` (float): 最高价，必填
- `low_price` (float): 最低价，必填
- `close_price` (float): 收盘价，必填
- `volume` (int): 成交量，必填
- `turnover` (float, 可选): 成交额

**示例：**
```python
price = StockPrice(
    symbol="000001",
    date=datetime(2024, 1, 15),
    open_price=12.50,
    high_price=12.80,
    low_price=12.30,
    close_price=12.65,
    volume=1000000,
    turnover=12500000.0
)
```

### NewsItem - 新闻条目模型

表示单条新闻信息。

**字段：**
- `title` (str): 新闻标题，必填
- `content` (str, 可选): 新闻内容
- `source` (str): 新闻来源，必填
- `publish_time` (datetime): 发布时间，必填
- `url` (str, 可选): 新闻链接
- `sentiment` (str, 可选): 情感倾向（positive/negative/neutral）

**示例：**
```python
news = NewsItem(
    title="平安银行发布三季度财报",
    content="平安银行今日发布三季度财报...",
    source="财经新闻网",
    publish_time=datetime.now(),
    url="https://example.com/news",
    sentiment="positive"
)
```

### TechnicalIndicators - 技术指标模型

表示股票的技术分析指标。

**字段：**
- `symbol` (str): 股票代码，必填
- `date` (datetime): 计算日期，必填
- `ma5` (float, 可选): 5日移动平均线
- `ma10` (float, 可选): 10日移动平均线
- `ma20` (float, 可选): 20日移动平均线
- `ma60` (float, 可选): 60日移动平均线
- `rsi` (float, 可选): 相对强弱指数
- `macd` (float, 可选): MACD指标
- `kdj_k` (float, 可选): KDJ指标K值
- `kdj_d` (float, 可选): KDJ指标D值
- `kdj_j` (float, 可选): KDJ指标J值

**示例：**
```python
indicators = TechnicalIndicators(
    symbol="000001",
    date=datetime.now(),
    ma5=12.50,
    ma20=12.30,
    rsi=65.5,
    macd=0.15
)
```

### AnalysisReport - 分析报告模型

表示完整的股票分析报告。

**主要字段：**
- `symbol` (str): 股票代码
- `stock_info` (StockInfo): 股票基本信息
- `analysis_date` (datetime): 分析日期
- `current_price` (float): 当前价格
- `price_change` (float): 价格变化
- `price_change_percent` (float): 价格变化百分比
- `technical_indicators` (TechnicalIndicators): 技术指标
- `trend_analysis` (str): 趋势分析
- `support_resistance` (Dict[str, float]): 支撑位和阻力位
- `fundamental_analysis` (str): 基本面分析
- `news_items` (List[NewsItem]): 相关新闻
- `news_sentiment` (str): 新闻整体情感倾向
- `news_summary` (str): 新闻摘要
- `overall_rating` (str): 综合评级（买入/持有/卖出）
- `risk_level` (str): 风险等级（低/中/高）
- `investment_advice` (str): 投资建议
- `ai_analysis` (str): AI生成的详细分析报告

## 数据验证

### 自动类型转换
pydantic会自动进行类型转换：
```python
# 字符串自动转换为浮点数
price = StockPrice(
    symbol="000001",
    date="2024-01-15",  # 字符串自动转换为datetime
    open_price="12.50",  # 字符串自动转换为float
    # ...
)
```

### 数据验证
模型会自动验证数据的有效性：
```python
# 这会抛出验证错误
try:
    invalid_price = StockPrice(
        symbol="",  # 空字符串无效
        date="invalid-date",  # 无效日期格式
        open_price="not-a-number"  # 无法转换为数字
    )
except ValidationError as e:
    print(e)
```

## JSON序列化

### 导出为JSON
```python
report = AnalysisReport(...)
json_data = report.model_dump_json()
```

### 从JSON导入
```python
json_str = '{"symbol": "000001", ...}'
report = AnalysisReport.model_validate_json(json_str)
```

### 自定义序列化
模型配置了自定义的JSON编码器：
```python
class Config:
    json_encoders = {
        datetime: lambda v: v.isoformat()
    }
```

## 使用最佳实践

1. **类型提示**: 利用pydantic的类型提示获得更好的IDE支持
2. **数据验证**: 依赖pydantic的自动验证，不需要手动检查数据类型
3. **序列化**: 使用内置的序列化方法进行JSON转换
4. **文档**: 为每个字段提供清晰的描述
5. **默认值**: 为可选字段提供合理的默认值
