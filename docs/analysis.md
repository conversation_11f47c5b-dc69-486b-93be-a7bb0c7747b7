# 分析引擎模块说明

## 概述

`analysis.py` 模块是整个金融分析系统的核心，负责整合股票数据和新闻信息，生成智能化的分析报告。包括技术分析、基本面分析、新闻情感分析和综合投资建议。

## 主要类

### AnalysisEngine

分析引擎类，提供完整的股票分析功能。

#### 主要方法

##### generate_analysis_report(symbol, exchange=None)

生成完整的股票分析报告

**参数：**
- `symbol` (str): 股票代码
- `exchange` (str, 可选): 交易所代码

**返回：**
- `AnalysisReport`: 完整的分析报告对象，包含所有分析结果

**示例：**
```python
engine = AnalysisEngine()
report = engine.generate_analysis_report("000001", "SZSE")
if report:
    print(f"股票名称: {report.stock_info.name}")
    print(f"当前价格: {report.current_price}")
    print(f"综合评级: {report.overall_rating}")
    print(f"投资建议: {report.investment_advice}")
```

## 分析流程

### 1. 数据收集阶段
- 获取股票基本信息（名称、交易所、行业、市值等）
- 获取历史价格数据（开盘价、收盘价、最高价、最低价、成交量）
- 计算技术指标（MA、RSI、MACD、KDJ等）
- 搜索相关新闻并进行情感分析

### 2. 分析计算阶段
- **价格变化分析**: 计算当前价格相对于前一交易日的变化
- **趋势分析**: 基于移动平均线和价格走势判断短期趋势
- **支撑阻力分析**: 计算近期的支撑位和阻力位
- **基本面分析**: 基于市值、行业、波动性等进行基本面评估
- **新闻情感分析**: 统计正面、负面、中性新闻的数量和整体情感倾向

### 3. 综合评级阶段
- **技术面评分**: 基于RSI、移动平均线等技术指标
- **情感面评分**: 基于新闻情感分析结果
- **风险评估**: 识别波动性风险、市值风险等
- **投资建议生成**: 综合各项评分给出买入/持有/卖出建议

### 4. AI分析生成阶段
- 调用Windmill部署的大语言模型
- 生成专业的投资分析报告
- 包含技术面、基本面、市场情绪、风险评估等内容

## 分析指标说明

### 技术分析指标

#### 移动平均线分析
- 价格与5日、20日均线的位置关系
- 均线的支撑和阻力作用
- 短期和中期趋势判断

#### RSI分析
- RSI > 70: 超买状态，可能面临回调
- RSI < 30: 超卖状态，可能存在反弹机会
- 30 ≤ RSI ≤ 70: 正常区间

#### MACD分析
- 用于判断趋势变化和买卖时机
- 金叉和死叉信号识别

### 基本面分析

#### 市值分析
- 大盘股（>1000亿）: 相对稳定，风险较低
- 中盘股（100-1000亿）: 具有成长性
- 小盘股（<100亿）: 成长潜力大但风险高

#### 波动性分析
- 基于最近10个交易日的价格标准差
- 高波动性（>5%）: 风险较高
- 中等波动性（2-5%）: 风险适中
- 低波动性（<2%）: 相对稳定

### 新闻情感分析

#### 情感分类
- **positive**: 利好消息，可能推动股价上涨
- **negative**: 利空消息，可能导致股价下跌
- **neutral**: 中性消息，对股价影响不明确

#### 情感得分计算
```
情感得分 = (正面新闻数 - 负面新闻数) / 总新闻数
```

## 投资评级系统

### 综合评分机制
- 技术面评分：基于RSI、均线位置等
- 情感面评分：基于新闻情感分析
- 总分范围：-5 到 +5

### 评级标准
- **买入** (≥3分): 技术面和基本面均显示积极信号
- **持有** (1-2分): 当前状态相对稳定
- **卖出** (≤0分): 存在较大风险

### 风险等级
- **低风险**: 无明显风险因素
- **中风险**: 存在1个风险因素
- **高风险**: 存在2个或以上风险因素

## AI分析报告

### Windmill集成
- 调用部署在Windmill上的大语言模型
- 生成专业的投资分析报告
- 包含具体的数据支撑和投资建议

### 分析内容
1. **技术面分析**: 基于价格走势和技术指标
2. **基本面分析**: 基于公司基本信息和行业状况
3. **市场情绪分析**: 基于新闻情感和市场反应
4. **风险评估**: 识别潜在风险因素
5. **投资建议**: 具体的操作建议

### 降级策略
当Windmill接口不可用时，系统会生成简化的分析报告：
- 基于技术指标的基础分析
- 简单的趋势判断
- 基础的投资建议

## 使用注意事项

1. **数据依赖**: 分析质量依赖于数据的完整性和准确性
2. **时效性**: 分析结果基于当前可获得的数据，具有时效性
3. **风险提示**: 所有分析结果仅供参考，不构成投资建议
4. **配置要求**: 需要正确配置Windmill接口以获得完整的AI分析
5. **网络依赖**: 需要稳定的网络连接以获取实时数据

## 错误处理

- 数据获取失败时会记录错误并返回None
- 部分数据缺失时会使用可用数据进行分析
- AI分析失败时会降级到简化分析
- 所有异常都会记录到日志中

## 性能优化

- 使用缓存机制减少重复数据请求
- 异步处理提高响应速度
- 合理的超时设置避免长时间等待
- 降级策略确保服务可用性
