# 真实金融数据配置指南

## 🎯 配置目标

将金融分析项目从模拟数据切换到真实市场数据，获得准确的股票价格、技术指标和市场信息。

## ✅ 已完成的配置

### 1. 股票数据源配置 (已完成)

**状态**: ✅ 成功配置
**数据源**: akshare (中国股票) + yfinance (国际股票)

**验证结果**:
- 平安银行(000001): 12.31元 (真实价格)
- 苹果(AAPL): $207.57 (真实价格)
- 技术指标基于真实数据计算

**配置步骤**:
```bash
# 1. 安装必要依赖
pip install akshare yfinance

# 2. 验证安装
python simple_data_test.py
```

### 2. 项目集成配置 (已完成)

**状态**: ✅ 成功集成
**功能**: 股票信息、价格数据、技术指标计算

**使用示例**:
```python
from financial_analysis import StockDataProvider

provider = StockDataProvider()

# 获取中国股票
stock_info = provider.get_stock_info("000001", "SZSE")
prices = provider.get_stock_prices("000001", days=30, exchange="SZSE")

# 获取美股
stock_info = provider.get_stock_info("AAPL", "NASDAQ")
prices = provider.get_stock_prices("AAPL", days=30, exchange="NASDAQ")
```

## ⚠️ 待完成的配置

### 3. 新闻数据源配置 (需要Windmill权限)

**状态**: ⚠️ 使用模拟数据
**原因**: Windmill token权限不足

**解决方案**:
1. 联系Windmill管理员
2. 为token添加权限: `run:script/f/gemini/js_structured_output`
3. 确认脚本路径正确

**当前配置**:
```env
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your_windmill_token_here  # 需要有效token
WINDMILL_WORKSPACE=my-workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=js_structured_output
```

### 4. AI分析配置 (需要Windmill权限)

**状态**: ⚠️ 使用简化版本
**原因**: 同新闻数据源问题

## 🚀 使用真实数据

### 基本分析 (已可用)

```python
from financial_analysis import AnalysisEngine

# 创建分析引擎
engine = AnalysisEngine()

# 生成分析报告 (股票数据为真实)
report = engine.generate_analysis_report("000001", "SZSE")

if report:
    print(f"股票名称: {report.stock_info.name}")
    print(f"当前价格: {report.current_price:.2f}")  # 真实价格
    print(f"技术指标: RSI={report.technical_indicators.rsi:.2f}")  # 基于真实数据
    print(f"投资建议: {report.investment_advice}")
```

### 批量分析

```python
# 分析多只股票
stocks = ["000001", "000002", "600036"]  # 中国股票
# stocks = ["AAPL", "MSFT", "GOOGL"]     # 美股

for symbol in stocks:
    report = engine.generate_analysis_report(symbol, "SZSE")  # 或 "NASDAQ"
    if report:
        print(f"{symbol}: {report.current_price:.2f} - {report.overall_rating}")
```

## 📊 数据质量验证

### 快速验证

```bash
# 运行简单测试
python simple_data_test.py

# 预期结果: 3/4 通过 (75%)
# - akshare直接测试: ✅ 通过
# - yfinance直接测试: ✅ 通过  
# - 项目股票数据集成: ✅ 通过
# - 项目新闻数据集成: ❌ 失败 (Windmill权限问题)
```

### 详细验证

```bash
# 运行完整验证 (修复日期问题后)
python verify_real_data.py
```

## 🔍 数据特征识别

### 真实数据特征
- **价格变化**: 符合市场规律，有合理的波动
- **时效性**: 数据日期为最近交易日
- **一致性**: 多个数据源价格基本一致
- **完整性**: 包含完整的OHLCV数据

### 模拟数据特征
- **价格固定**: 基于算法生成，变化规律
- **内容重复**: 新闻标题和内容模板化
- **日志提示**: 出现"模拟"、"mock"等关键词

## 🛠️ 故障排除

### 常见问题

1. **股票数据异常**
   ```bash
   # 检查依赖安装
   pip list | grep -E "(akshare|yfinance)"
   
   # 重新安装
   pip install --upgrade akshare yfinance
   ```

2. **网络连接问题**
   ```bash
   # 测试网络连接
   ping finance.yahoo.com
   ping push2.eastmoney.com
   ```

3. **API限制**
   - akshare: 注意调用频率限制
   - yfinance: 避免过于频繁的请求

### 日志分析

关键日志信息：
- `✓ 股票数据提供者初始化完成` - 正常
- `akshare不可用，中国股票数据将使用模拟数据` - 需要安装akshare
- `Windmill配置不完整` - 需要配置Windmill

## 📈 性能优化

### 缓存配置
```env
DATA_CACHE_DURATION=300  # 5分钟缓存
```

### 批量请求
```python
# 避免频繁单次请求
symbols = ["000001", "000002", "600036"]
reports = []
for symbol in symbols:
    report = engine.generate_analysis_report(symbol, "SZSE")
    reports.append(report)
    time.sleep(1)  # 避免请求过快
```

## 🎉 成功标志

当您看到以下结果时，说明真实数据配置成功：

1. **股票价格实时变化** - 不是固定的模拟值
2. **技术指标合理** - RSI在0-100之间，MACD有正负变化
3. **公司信息准确** - 市值、行业等信息正确
4. **测试通过率高** - simple_data_test.py显示75%以上通过率

## 📚 相关文档

- [故障排除指南](troubleshooting.md)
- [API接口文档](api.md)
- [使用示例](examples.md)

---

**最后更新**: 2025-08-01
**配置状态**: 股票数据✅ | 新闻数据⚠️ | AI分析⚠️
