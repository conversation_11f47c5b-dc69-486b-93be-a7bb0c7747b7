# 金融证券分析项目文档

## 项目概述

本项目是一个基于人工智能技术的金融证券分析工具，主要功能包括：

- 股票数据获取和分析
- 基于大语言模型的新闻情感分析
- 智能化分析报告生成
- 多交易所股票代码支持

## 模块说明

### 核心模块

- `config.py` - 项目配置管理
- `models.py` - 数据模型定义
- `utils.py` - 工具函数
- `stock_data.py` - 股票数据获取模块
- `news_search.py` - 新闻搜索模块
- `analysis.py` - 分析引擎模块
- `main.py` - 主程序入口

### 文档结构

- `config.md` - 配置模块说明
- `models.md` - 数据模型说明
- `stock_data.md` - 股票数据模块说明
- `news_search.md` - 新闻搜索模块说明
- `analysis.md` - 分析引擎说明
- `api.md` - API接口说明
- `examples.md` - 使用示例

## 快速开始

1. 安装依赖：`pip install -r requirements.txt`
2. 配置环境变量：复制`.env.example`为`.env`并填写相关配置
3. 运行分析：`python -m financial_analysis 000001`

## 技术栈

- Python 3.8+
- pandas, numpy - 数据处理
- yfinance, akshare - 金融数据获取
- pydantic - 数据验证
- loguru - 日志管理
- requests - HTTP请求
