# Windmill 异步客户端使用指南

## 概述

Windmill 异步客户端是一个基于 Python asyncio 的异步 HTTP 客户端，用于与 Windmill 工作流平台进行交互。它提供了完整的异步作业执行功能，包括作业触发、状态轮询和结果获取。

## 主要特性

- **异步执行**: 基于 asyncio 和 aiohttp，支持高并发作业处理
- **自动轮询**: 自动轮询作业状态直到完成或超时
- **错误处理**: 完善的错误处理和重试机制
- **配置灵活**: 支持从配置文件或直接参数初始化
- **类型安全**: 完整的类型注解支持

## 安装依赖

```bash
pip install aiohttp>=3.8.0
```

## 基本配置

在 `.env` 文件中配置 Windmill 服务信息：

```env
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your_access_token
WINDMILL_WORKSPACE=my-workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=text_generation
```

## 快速开始

### 1. 导入客户端

```python
import asyncio
from financial_analysis.windmill_client import WindmillClient, windmill_client
```

### 2. 基本使用

```python
async def basic_example():
    # 使用全局客户端实例
    result = await windmill_client.execute_job(
        folder="gemini",
        script="text_generation",
        payload={"prompt": "分析苹果公司股票"}
    )
    
    if result:
        print(f"作业完成: {result}")
    else:
        print("作业执行失败")

# 运行异步函数
asyncio.run(basic_example())
```

### 3. 自定义客户端

```python
async def custom_client_example():
    # 创建自定义配置的客户端
    client = WindmillClient(
        base_url="https://custom.windmill.com",
        token="custom_token",
        workspace="custom_workspace"
    )
    
    result = await client.execute_job("folder", "script", {"param": "value"})
    return result
```

## 核心功能

### 1. 触发作业

```python
async def trigger_job_example():
    client = WindmillClient()
    
    # 触发作业并获取UUID
    job_uuid = await client.trigger_job(
        folder="analysis",
        script="stock_analysis",
        payload={
            "symbol": "AAPL",
            "days": 30
        }
    )
    
    if job_uuid:
        print(f"作业已触发: {job_uuid}")
    else:
        print("作业触发失败")
```

### 2. 等待作业完成

```python
async def wait_completion_example():
    client = WindmillClient()
    
    # 等待作业完成
    result = await client.wait_for_job_completion(
        job_uuid="your-job-uuid",
        max_wait_time=300,  # 最大等待5分钟
        poll_interval=2     # 每2秒轮询一次
    )
    
    if result and result.get('completed'):
        print(f"作业完成: {result['result']}")
    else:
        print("作业未完成或超时")
```

### 3. 完整作业执行

```python
async def complete_job_example():
    client = WindmillClient()
    
    # 执行完整的作业流程：触发 -> 等待 -> 获取结果
    result = await client.execute_job(
        folder="gemini",
        script="text_generation",
        payload={
            "prompt": "请分析当前市场趋势",
            "system_instruction": "你是专业的金融分析师"
        },
        max_wait_time=120,  # 最大等待2分钟
        poll_interval=3     # 每3秒轮询一次
    )
    
    return result
```

### 4. 文本分析生成

```python
async def text_analysis_example():
    # 使用便捷方法生成文本分析
    analysis = await windmill_client.generate_text_analysis(
        prompt="请分析苹果公司的投资价值",
        system_instruction="你是专业的金融分析师",
        search=True  # 启用搜索功能以获取最新市场信息
    )

    if analysis:
        print(f"分析结果: {analysis}")
```

## 高级用法

### 1. 并发作业处理

```python
async def batch_jobs_example():
    client = WindmillClient()
    
    # 准备多个作业
    jobs = [
        {"folder": "analysis", "script": "sentiment", "payload": {"text": "市场看好"}},
        {"folder": "analysis", "script": "sentiment", "payload": {"text": "风险较高"}},
        {"folder": "analysis", "script": "sentiment", "payload": {"text": "保持观望"}},
    ]
    
    # 并发执行所有作业
    tasks = [
        client.execute_job(job["folder"], job["script"], job["payload"])
        for job in jobs
    ]
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"作业 {i+1} 失败: {result}")
        else:
            print(f"作业 {i+1} 完成: {result}")
```

### 2. 错误处理和重试

```python
async def robust_job_execution():
    client = WindmillClient()
    
    max_retries = 3
    retry_delay = 5  # 秒
    
    for attempt in range(max_retries):
        try:
            result = await client.execute_job(
                folder="analysis",
                script="complex_task",
                payload={"data": "complex_data"},
                max_wait_time=180
            )
            
            if result:
                return result
            else:
                print(f"尝试 {attempt + 1} 失败，作业未完成")
                
        except Exception as e:
            print(f"尝试 {attempt + 1} 异常: {e}")
            
        if attempt < max_retries - 1:
            print(f"等待 {retry_delay} 秒后重试...")
            await asyncio.sleep(retry_delay)
    
    print("所有重试都失败了")
    return None
```

### 3. 超时和取消处理

```python
async def timeout_handling_example():
    client = WindmillClient()
    
    try:
        # 使用 asyncio.wait_for 设置总超时
        result = await asyncio.wait_for(
            client.execute_job("folder", "script", {"param": "value"}),
            timeout=60.0  # 总超时60秒
        )
        
        return result
        
    except asyncio.TimeoutError:
        print("操作超时")
        return None
```

## 配置选项

### 客户端配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `base_url` | str | 从配置读取 | Windmill 服务基础URL |
| `token` | str | 从配置读取 | 访问令牌 |
| `workspace` | str | 从配置读取 | 工作空间名称 |

### 作业执行配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `max_wait_time` | int | 300 | 最大等待时间（秒） |
| `poll_interval` | int | 1 | 轮询间隔（秒） |

## 错误处理

客户端提供了完善的错误处理机制：

### 1. 网络错误

```python
async def handle_network_errors():
    try:
        result = await windmill_client.execute_job("folder", "script")
    except aiohttp.ClientError as e:
        print(f"网络错误: {e}")
    except asyncio.TimeoutError:
        print("请求超时")
```

### 2. 作业执行错误

```python
async def handle_job_errors():
    result = await windmill_client.execute_job("folder", "script")
    
    if not result:
        print("作业执行失败")
    elif not result.get('completed'):
        print("作业未完成")
    elif 'error' in result:
        print(f"作业执行错误: {result['error']}")
```

## 最佳实践

### 1. 资源管理

```python
# 推荐：使用全局客户端实例
from financial_analysis.windmill_client import windmill_client

async def recommended_usage():
    result = await windmill_client.execute_job("folder", "script")
    return result
```

### 2. 超时设置

```python
# 根据作业复杂度设置合适的超时时间
async def timeout_best_practice():
    # 简单作业：30-60秒
    simple_result = await windmill_client.execute_job(
        "folder", "simple_script", max_wait_time=60
    )
    
    # 复杂作业：2-5分钟
    complex_result = await windmill_client.execute_job(
        "folder", "complex_script", max_wait_time=300
    )
```

### 3. 日志记录

客户端内置了详细的日志记录，使用 loguru 库：

```python
from loguru import logger

# 调整日志级别查看详细信息
logger.add("windmill_client.log", level="DEBUG")
```

## 故障排除

### 1. 配置问题

- 检查 `.env` 文件中的配置是否正确
- 验证 Windmill 服务是否可访问
- 确认访问令牌是否有效

### 2. 网络问题

- 检查网络连接
- 验证防火墙设置
- 确认 Windmill 服务状态

### 3. 作业执行问题

- 检查作业参数是否正确
- 验证脚本是否存在
- 查看 Windmill 服务日志

## 示例代码

完整的示例代码请参考：
- `examples/windmill_async_example.py` - 各种使用场景的示例
- `tests/test_windmill_client.py` - 单元测试示例

## 相关文档

- [Windmill API 文档](https://docs.windmill.dev/docs/core_concepts/api)
- [aiohttp 文档](https://docs.aiohttp.org/)
- [asyncio 文档](https://docs.python.org/3/library/asyncio.html)
