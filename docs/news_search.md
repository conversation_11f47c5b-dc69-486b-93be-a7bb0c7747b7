# 新闻搜索模块说明

## 概述

`news_search.py` 模块通过Gemini大语言模型和Windmill部署的接口搜索股票相关新闻，并进行情感分析和内容摘要。

## 主要类

### NewsSearcher

新闻搜索器类，提供股票新闻搜索、情感分析和摘要生成功能。

#### 主要方法

##### search_stock_news(stock_info, days=None)

搜索股票相关新闻

**参数：**
- `stock_info` (StockInfo): 股票基本信息对象
- `days` (int, 可选): 搜索天数，默认使用配置中的值

**返回：**
- `List[NewsItem]`: 新闻条目列表，每个条目包含标题、内容、来源、发布时间、情感倾向等信息

**示例：**
```python
searcher = NewsSearcher()
stock_info = StockInfo(symbol="000001", name="平安银行", exchange="SZSE")
news_items = searcher.search_stock_news(stock_info, days=7)
for news in news_items:
    print(f"{news.title} - {news.sentiment}")
```

##### analyze_news_sentiment(news_items)

分析新闻情感倾向

**参数：**
- `news_items` (List[NewsItem]): 新闻条目列表

**返回：**
- `Dict[str, Any]`: 情感分析结果，包含：
  - `overall_sentiment`: 整体情感（positive/negative/neutral）
  - `positive_count`: 正面新闻数量
  - `negative_count`: 负面新闻数量
  - `neutral_count`: 中性新闻数量
  - `sentiment_score`: 情感得分（-1到1之间）

**示例：**
```python
sentiment_result = searcher.analyze_news_sentiment(news_items)
print(f"整体情感: {sentiment_result['overall_sentiment']}")
print(f"情感得分: {sentiment_result['sentiment_score']}")
```

##### generate_news_summary(news_items, stock_info)

生成新闻摘要

**参数：**
- `news_items` (List[NewsItem]): 新闻条目列表
- `stock_info` (StockInfo): 股票基本信息

**返回：**
- `str`: 新闻摘要文本

**示例：**
```python
summary = searcher.generate_news_summary(news_items, stock_info)
print(f"新闻摘要: {summary}")
```

## Windmill集成

### 配置要求

在 `.env` 文件中配置以下参数：

```env
WINDMILL_BASE_URL=your_windmill_base_url
WINDMILL_TOKEN=your_windmill_token
WINDMILL_WORKSPACE=my_workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=js_structured_output
```

### API调用

模块会调用Windmill部署的大语言模型接口：

**接口路径：** `/api/w/{workspace}/{folder}/{script}`

**请求参数：**
- `prompt`: 提示词（搜索查询或摘要请求）
- `system_instruction`: 系统提示词
- `responseSchema`: 结构化输出格式

### 响应格式

#### 新闻搜索响应
```json
{
  "news_items": [
    {
      "title": "新闻标题",
      "content": "新闻内容",
      "source": "新闻来源",
      "publish_time": "2024-01-01T12:00:00Z",
      "url": "新闻链接",
      "sentiment": "positive"
    }
  ]
}
```

#### 摘要生成响应
```json
{
  "summary": "生成的新闻摘要文本"
}
```

## 情感分析

### 情感分类
- **positive**: 正面情感，对股票价格有积极影响的新闻
- **negative**: 负面情感，对股票价格有消极影响的新闻
- **neutral**: 中性情感，对股票价格影响不明确的新闻

### 情感得分计算
```
情感得分 = (正面新闻数量 - 负面新闻数量) / 总新闻数量
```

### 整体情感判断
- 得分 > 0.2: 整体正面
- 得分 < -0.2: 整体负面
- -0.2 ≤ 得分 ≤ 0.2: 整体中性

## 缓存机制

- **缓存键格式**: `news_{股票代码}_{搜索天数}`
- **缓存时间**: 由配置文件中的 `data_cache_duration` 参数控制
- **缓存内容**: 搜索到的新闻条目列表

## 降级策略

当Windmill接口不可用时，模块会自动降级到模拟数据模式：

1. 生成3条模拟新闻（1条正面、1条负面、1条中性）
2. 返回简单的摘要文本
3. 记录警告日志

## 错误处理

- 网络请求超时：30秒超时限制
- API调用失败：自动降级到模拟数据
- 数据解析错误：跳过有问题的新闻条目
- 所有异常都会记录到日志中

## 注意事项

1. 需要有效的Windmill配置才能获取真实新闻数据
2. API调用有频率限制，建议合理使用缓存
3. 新闻搜索结果的质量依赖于大语言模型的能力
4. 情感分析结果仅供参考，不构成投资建议
5. 模拟数据仅用于测试和演示，不应用于实际投资决策
