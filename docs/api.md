# API接口说明

## 概述

本文档描述了金融证券分析项目的主要API接口，包括各个模块的公共方法和使用示例。

## 核心API

### StockDataProvider API

股票数据获取接口。

#### get_stock_info(symbol, exchange=None)

获取股票基本信息。

**参数：**
- `symbol` (str): 股票代码
- `exchange` (str, 可选): 交易所代码

**返回：**
- `StockInfo | None`: 股票基本信息对象

**示例：**
```python
from financial_analysis import StockDataProvider

provider = StockDataProvider()
info = provider.get_stock_info("000001", "SZSE")
if info:
    print(f"股票名称: {info.name}")
```

#### get_stock_prices(symbol, days=30, exchange=None)

获取股票历史价格数据。

**参数：**
- `symbol` (str): 股票代码
- `days` (int): 获取天数，默认30
- `exchange` (str, 可选): 交易所代码

**返回：**
- `List[StockPrice]`: 价格数据列表

**示例：**
```python
prices = provider.get_stock_prices("000001", days=7)
for price in prices:
    print(f"{price.date}: {price.close_price}")
```

#### get_technical_indicators(symbol, exchange=None)

获取技术指标。

**参数：**
- `symbol` (str): 股票代码
- `exchange` (str, 可选): 交易所代码

**返回：**
- `TechnicalIndicators | None`: 技术指标对象

### NewsSearcher API

新闻搜索接口。

#### search_stock_news(stock_info, days=None)

搜索股票相关新闻。

**参数：**
- `stock_info` (StockInfo): 股票基本信息
- `days` (int, 可选): 搜索天数

**返回：**
- `List[NewsItem]`: 新闻条目列表

**示例：**
```python
from financial_analysis import NewsSearcher, StockInfo

searcher = NewsSearcher()
stock_info = StockInfo(symbol="000001", name="平安银行", exchange="SZSE")
news = searcher.search_stock_news(stock_info, days=7)
```

#### analyze_news_sentiment(news_items)

分析新闻情感倾向。

**参数：**
- `news_items` (List[NewsItem]): 新闻条目列表

**返回：**
- `Dict[str, Any]`: 情感分析结果

#### generate_news_summary(news_items, stock_info)

生成新闻摘要。

**参数：**
- `news_items` (List[NewsItem]): 新闻条目列表
- `stock_info` (StockInfo): 股票基本信息

**返回：**
- `str`: 新闻摘要文本

### AnalysisEngine API

分析引擎接口。

#### generate_analysis_report(symbol, exchange=None)

生成完整的分析报告。

**参数：**
- `symbol` (str): 股票代码
- `exchange` (str, 可选): 交易所代码

**返回：**
- `AnalysisReport | None`: 分析报告对象

**示例：**
```python
from financial_analysis import AnalysisEngine

engine = AnalysisEngine()
report = engine.generate_analysis_report("000001")
if report:
    print(f"综合评级: {report.overall_rating}")
    print(f"投资建议: {report.investment_advice}")
```

## 命令行API

### 基本命令

#### analyze - 分析单个股票
```bash
financial-analysis analyze 000001
financial-analysis analyze AAPL --exchange NASDAQ
financial-analysis analyze 000001 --output json --file report.json
```

#### batch - 批量分析
```bash
financial-analysis batch stocks.txt
financial-analysis batch stocks.txt --output json --output-dir reports/
```

#### interactive - 交互模式
```bash
financial-analysis interactive
```

### 命令行参数

#### 通用参数
- `--output, -o`: 输出格式（text/json）
- `--verbose, -v`: 详细输出
- `--help, -h`: 显示帮助信息

#### analyze命令参数
- `symbol`: 股票代码（必填）
- `--exchange, -e`: 交易所代码
- `--file, -f`: 输出文件路径

#### batch命令参数
- `file`: 股票代码文件路径（必填）
- `--output-dir, -d`: 输出目录

## 配置API

### 环境变量配置

```python
import os
from financial_analysis.config import settings

# 通过环境变量设置
os.environ['WINDMILL_BASE_URL'] = 'https://wm.atjog.com'
os.environ['WINDMILL_TOKEN'] = 'your_windmill_token'
os.environ['LOG_LEVEL'] = 'DEBUG'

# 访问配置
base_url = settings.windmill_base_url
token = settings.windmill_token
log_level = settings.log_level
```

### .env文件配置

创建`.env`文件：
```env
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your_windmill_token
WINDMILL_WORKSPACE=my-workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=js_structured_output
LOG_LEVEL=INFO
```

## 错误处理

### 异常类型

所有API方法都会处理异常并记录日志，通常返回None或空列表而不是抛出异常。

### 日志记录

```python
from loguru import logger

# 所有模块都使用loguru进行日志记录
logger.info("信息日志")
logger.warning("警告日志")
logger.error("错误日志")
```

### 错误检查

```python
# 检查返回值
report = engine.generate_analysis_report("INVALID")
if report is None:
    print("分析失败")
else:
    print("分析成功")
```

## 数据格式

### JSON输出格式

```json
{
  "symbol": "000001",
  "stock_info": {
    "name": "平安银行",
    "exchange": "SZSE",
    "currency": "CNY"
  },
  "analysis_date": "2024-01-15T10:30:00",
  "price_data": {
    "current_price": 12.65,
    "price_change": 0.15,
    "price_change_percent": 1.2
  },
  "investment_advice": {
    "overall_rating": "持有",
    "risk_level": "中",
    "advice": "技术面相对稳定，建议继续持有观察"
  }
}
```

### 批量文件格式

股票代码文件格式（每行一个股票代码）：
```
000001
000002:SZSE
AAPL:NASDAQ
TSLA:NASDAQ
```

## 使用示例

### 完整分析流程

```python
from financial_analysis import AnalysisEngine

# 创建分析引擎
engine = AnalysisEngine()

# 生成分析报告
report = engine.generate_analysis_report("000001", "SZSE")

if report:
    # 输出基本信息
    print(f"股票: {report.stock_info.name}")
    print(f"当前价格: {report.current_price}")
    print(f"评级: {report.overall_rating}")
    
    # 输出技术指标
    indicators = report.technical_indicators
    print(f"RSI: {indicators.rsi}")
    print(f"MACD: {indicators.macd}")
    
    # 输出新闻分析
    print(f"新闻情感: {report.news_sentiment}")
    print(f"新闻摘要: {report.news_summary}")
    
    # 输出投资建议
    print(f"投资建议: {report.investment_advice}")
else:
    print("分析失败")
```

### 自定义分析

```python
from financial_analysis import StockDataProvider, NewsSearcher

# 分别使用各个模块
data_provider = StockDataProvider()
news_searcher = NewsSearcher()

# 获取股票信息
stock_info = data_provider.get_stock_info("000001")
if stock_info:
    # 获取价格数据
    prices = data_provider.get_stock_prices("000001", days=30)
    
    # 搜索新闻
    news = news_searcher.search_stock_news(stock_info)
    
    # 分析新闻情感
    sentiment = news_searcher.analyze_news_sentiment(news)
    
    print(f"获取到 {len(prices)} 天价格数据")
    print(f"获取到 {len(news)} 条新闻")
    print(f"新闻整体情感: {sentiment['overall_sentiment']}")
```
