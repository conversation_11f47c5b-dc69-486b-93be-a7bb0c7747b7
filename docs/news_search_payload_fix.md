# 新闻搜索模块 Payload 参数修复说明

## 概述

本次修复解决了 `financial_analysis/news_search.py` 文件中 `_async_search_via_windmill` 和 `_async_generate_summary_via_windmill` 方法中的 payload 参数问题。根据 Windmill 客户端的最新更新，移除了 `responseSchema` 参数，添加了 `search` 参数。

## 问题描述

在之前的实现中，新闻搜索模块的两个异步方法仍在使用已废弃的 `responseSchema` 参数：

1. `_async_search_via_windmill` - 新闻搜索方法
2. `_async_generate_summary_via_windmill` - 新闻摘要生成方法

这导致与最新的 Windmill 客户端 API 不兼容。

## 修复内容

### 1. 新闻搜索方法修复

**修复前:**
```python
# 定义响应结构
response_schema = {
    "type": "object",
    "properties": {
        "news_items": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "content": {"type": "string"},
                    "source": {"type": "string"},
                    "publish_time": {"type": "string"},
                    "url": {"type": "string"},
                    "sentiment": {"type": "string", "enum": ["positive", "negative", "neutral"]}
                },
                "required": ["title", "source", "publish_time", "sentiment"]
            }
        }
    }
}

# 构建请求参数
payload = {
    "prompt": search_query,
    "system_instruction": "你是一个专业的金融新闻搜索助手。请搜索相关的股票新闻，并分析每条新闻的情感倾向（positive/negative/neutral）。",
    "responseSchema": response_schema
}
```

**修复后:**
```python
# 构建请求参数 - 移除 responseSchema，添加 search 参数
payload = {
    "prompt": search_query,
    "system_instruction": "你是一个专业的金融新闻搜索助手。请搜索相关的股票新闻，并分析每条新闻的情感倾向（positive/negative/neutral）。返回JSON格式，包含news_items数组，每个条目包含title、content、source、publish_time、url、sentiment字段。",
    "search": True  # 启用搜索功能以获取最新新闻
}
```

### 2. 新闻摘要生成方法修复

**修复前:**
```python
# 定义响应结构
response_schema = {
    "type": "object",
    "properties": {
        "summary": {"type": "string"}
    },
    "required": ["summary"]
}

# 构建请求参数
payload = {
    "prompt": prompt,
    "system_instruction": "你是一个专业的金融分析师，擅长总结和分析金融新闻。",
    "responseSchema": response_schema
}
```

**修复后:**
```python
# 构建请求参数 - 移除 responseSchema，添加 search 参数
payload = {
    "prompt": prompt,
    "system_instruction": "你是一个专业的金融分析师，擅长总结和分析金融新闻。请直接返回摘要文本。",
    "search": False  # 摘要生成不需要搜索功能
}
```

### 3. 响应解析逻辑更新

由于移除了 `responseSchema` 参数，响应格式可能会发生变化。更新了解析逻辑以支持：

1. **标准 Gemini API 格式** - 通过 `candidates[0].content.parts[0].text` 路径获取内容
2. **向后兼容格式** - 支持直接的 `news_items` 或 `summary` 字段
3. **字符串格式** - 直接返回字符串结果

#### 新闻搜索结果解析
```python
# 处理 Gemini API 的标准响应格式
if isinstance(analysis_result, dict):
    # 1. 检查 candidates 字段（标准 Gemini API 格式）
    if 'candidates' in analysis_result and analysis_result['candidates']:
        candidates = analysis_result['candidates']
        if len(candidates) > 0:
            candidate = candidates[0]
            
            # 提取 content.parts[0].text
            if ('content' in candidate and
                'parts' in candidate['content'] and
                candidate['content']['parts']):
                parts = candidate['content']['parts']
                if len(parts) > 0 and 'text' in parts[0]:
                    text_content = parts[0]['text']
                    # 尝试解析JSON格式的新闻数据
                    try:
                        import json
                        parsed_data = json.loads(text_content)
                        if isinstance(parsed_data, dict) and 'news_items' in parsed_data:
                            news_data = parsed_data['news_items']
                        elif isinstance(parsed_data, list):
                            news_data = parsed_data
                    except json.JSONDecodeError:
                        logger.warning("无法解析新闻搜索结果的JSON格式")
                        return []
    
    # 2. 检查直接的 news_items 字段（向后兼容）
    elif 'news_items' in analysis_result:
        news_data = analysis_result['news_items']
    
    # 3. 检查是否整个结果就是新闻数组
    elif isinstance(analysis_result, list):
        news_data = analysis_result
```

#### 新闻摘要结果解析
```python
# 处理 Gemini API 的标准响应格式
if isinstance(analysis_result, dict):
    # 1. 检查 candidates 字段（标准 Gemini API 格式）
    if 'candidates' in analysis_result and analysis_result['candidates']:
        candidates = analysis_result['candidates']
        if len(candidates) > 0:
            candidate = candidates[0]
            
            # 提取 content.parts[0].text
            if ('content' in candidate and
                'parts' in candidate['content'] and
                candidate['content']['parts']):
                parts = candidate['content']['parts']
                if len(parts) > 0 and 'text' in parts[0]:
                    summary = parts[0]['text']
    
    # 2. 检查直接的 summary 字段（向后兼容）
    elif 'summary' in analysis_result:
        summary = analysis_result['summary']
        
# 3. 如果结果直接是字符串
elif isinstance(analysis_result, str):
    summary = analysis_result
```

## 主要改进

### 1. 参数标准化
- **移除**: `responseSchema` 参数（已废弃）
- **添加**: `search` 参数（布尔值，控制是否启用搜索功能）

### 2. 搜索功能优化
- 新闻搜索: `search=True` - 启用搜索以获取最新新闻
- 摘要生成: `search=False` - 摘要生成不需要搜索功能

### 3. 响应解析增强
- 支持标准 Gemini API 响应格式
- 保持向后兼容性
- 增强错误处理和日志记录

### 4. 提示词优化
- 在系统指令中明确指定期望的输出格式
- 为摘要生成添加了"直接返回摘要文本"的要求

## 测试验证

创建了完整的测试脚本 `test_news_search_fix.py` 验证：

1. ✅ 新闻搜索 payload 参数正确性
2. ✅ 新闻摘要生成 payload 参数正确性  
3. ✅ 响应解析逻辑正确性
4. ✅ 向后兼容性

所有测试均通过，确保修复的正确性和稳定性。

## 影响范围

### 直接影响
- `financial_analysis/news_search.py` - 主要修改文件

### 间接影响
- 所有使用新闻搜索功能的模块将受益于更准确的搜索结果
- 提高了与 Windmill + Gemini API 的兼容性

## 向后兼容性

此次修复保持了完全的向后兼容性：
- 保持了相同的方法签名
- 保持了相同的返回值类型
- 支持多种响应格式的解析

## 技术细节

### Windmill + Gemini API 响应结构
```
result
├── candidates[]
│   └── content
│       └── parts[]
│           └── text (实际的文本内容)
├── grounding_metadata (搜索结果元数据，当启用搜索时)
└── usage_metadata (使用统计信息)
```

### 错误处理
- JSON 解析错误的优雅处理
- 详细的调试日志记录
- 降级到模拟数据的机制

## 事件循环问题修复

在修复过程中，还发现并解决了同步方法调用异步方法时的事件循环冲突问题：

### 问题描述
当在已有事件循环的环境中调用 `asyncio.run()` 时，会出现 "asyncio.run() cannot be called from a running event loop" 错误。

### 解决方案
实现了智能的事件循环检测和处理机制：

```python
try:
    # 尝试获取当前事件循环
    loop = asyncio.get_running_loop()
    # 如果已经在事件循环中，创建新的线程来运行异步代码
    import concurrent.futures
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future = executor.submit(asyncio.run, self._async_search_via_windmill(search_query, stock_info))
        return future.result()
except RuntimeError:
    # 没有运行中的事件循环，可以直接使用 asyncio.run
    return asyncio.run(self._async_search_via_windmill(search_query, stock_info))
```

这种方法确保了代码在各种环境下都能正常工作，无论是在同步环境还是异步环境中。

## 总结

本次修复成功解决了新闻搜索模块中的多个问题：

1. **Payload 参数兼容性** - 移除废弃的 `responseSchema` 参数，添加 `search` 参数
2. **响应解析增强** - 支持多种响应格式，提高解析成功率
3. **事件循环兼容性** - 解决同步/异步调用冲突问题
4. **向后兼容性** - 保持对旧格式的支持

通过这些修复，确保了与最新 Windmill 客户端 API 的完全兼容，提升了系统的稳定性和准确性。
