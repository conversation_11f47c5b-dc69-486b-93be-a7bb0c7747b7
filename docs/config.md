# 配置模块说明

## 概述

`config.py` 模块负责管理项目的所有配置参数，包括API密钥、数据源配置、日志配置等。使用pydantic-settings进行配置管理，支持从环境变量和.env文件读取配置。

## 主要类

### Settings

项目配置类，继承自BaseSettings，提供类型安全的配置管理。

## 配置参数

### Gemini API配置
- `windmill_base_url` (str): Windmill服务地址，必填
- `windmill_token` (str): Windmill API令牌，必填
- `windmill_workspace` (str): Windmill工作空间，必填
- `windmill_folder` (str): Windmill文件夹，必填
- `windmill_script` (str): Windmill脚本名称，必填
- `gemini_model` (str): Gemini模型名称，默认"gemini-pro"

### Windmill配置
- `windmill_base_url` (str, 可选): Windmill服务基础URL
- `windmill_token` (str, 可选): Windmill访问令牌
- `windmill_workspace` (str): Windmill工作空间，默认"my_workspace"
- `windmill_folder` (str): Windmill文件夹，默认"gemini"
- `windmill_script` (str): Windmill脚本名称，默认"js_structured_output"

### 日志配置
- `log_level` (str): 日志级别，默认"INFO"
- `log_file` (str): 日志文件路径，默认"logs/financial_analysis.log"

### 数据源配置
- `default_exchange` (str): 默认交易所，默认"SSE"
- `data_cache_duration` (int): 数据缓存时间（秒），默认300

### 分析配置
- `analysis_days` (int): 分析的历史天数，默认30
- `news_search_days` (int): 新闻搜索的天数范围，默认7

## 配置文件

### .env文件格式
```env
# Gemini API配置
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro

# Windmill配置
WINDMILL_BASE_URL=your_windmill_base_url
WINDMILL_TOKEN=your_windmill_token

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/financial_analysis.log

# 数据源配置
DEFAULT_EXCHANGE=SSE
DATA_CACHE_DURATION=300

# 分析配置
ANALYSIS_DAYS=30
NEWS_SEARCH_DAYS=7
```

## 使用方法

### 基本使用
```python
from financial_analysis.config import settings

# 获取配置值
base_url = settings.windmill_base_url
token = settings.windmill_token
log_level = settings.log_level
cache_duration = settings.data_cache_duration
```

### 环境变量覆盖
配置支持通过环境变量覆盖：
```bash
export WINDMILL_TOKEN="new_windmill_token"
export LOG_LEVEL="DEBUG"
```

### 配置验证
pydantic会自动进行类型验证：
- 必填字段缺失时会抛出异常
- 类型不匹配时会自动转换或抛出异常
- 提供字段描述和默认值

## 全局配置实例

模块提供了一个全局配置实例：
```python
from financial_analysis.config import settings
```

这个实例在模块导入时自动创建，可以在整个项目中使用。

## 注意事项

1. **敏感信息**: API密钥等敏感信息应通过环境变量或.env文件设置，不要硬编码在代码中
2. **文件路径**: 日志文件路径等应使用相对路径或绝对路径
3. **类型安全**: 利用pydantic的类型验证确保配置的正确性
4. **环境隔离**: 不同环境（开发、测试、生产）应使用不同的配置文件
