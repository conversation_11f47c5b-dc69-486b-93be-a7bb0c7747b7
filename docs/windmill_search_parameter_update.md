# Windmill 客户端搜索参数更新

## 概述

本次更新对 `financial_analysis/windmill_client.py` 中的 `generate_text_analysis` 方法进行了重要修改，移除了 `response_schema` 参数，新增了 `search` 布尔参数，以更好地支持 Windmill + Gemini API 的搜索功能。

## 主要变更

### 1. 方法签名修改

**修改前:**
```python
async def generate_text_analysis(self, prompt: str, system_instruction: str = None,
                               response_schema: Dict[str, Any] = None) -> Optional[str]:
```

**修改后:**
```python
async def generate_text_analysis(self, prompt: str, system_instruction: str = None,
                               search: bool = False) -> Optional[str]:
```

### 2. 参数说明

- **移除参数**: `response_schema` - 不再需要手动定义响应结构，Windmill 会自动处理
- **新增参数**: `search` - 布尔值，当需要进行搜索时设置为 `True`

### 3. 功能改进

#### 3.1 搜索功能集成
- 当 `search=True` 时，Windmill 会启用搜索功能，获取最新的市场信息
- 当 `search=False` 时，使用标准的文本生成功能

#### 3.2 响应解析优化
改进了响应解析逻辑，能够更好地处理 Windmill + Gemini API 的复杂响应结构：

```python
# 1. 优先检查 candidates 字段（标准 Gemini API 格式）
if 'candidates' in analysis_result and analysis_result['candidates']:
    candidates = analysis_result['candidates']
    if len(candidates) > 0:
        candidate = candidates[0]  # 取第一个候选响应
        
        # 提取 content.parts[0].text
        if ('content' in candidate and 
            'parts' in candidate['content'] and 
            candidate['content']['parts']):
            parts = candidate['content']['parts']
            if len(parts) > 0 and 'text' in parts[0]:
                text_content = parts[0]['text']
                if text_content:  # 确保文本内容不为空
                    return text_content
```

#### 3.3 错误处理增强
- 添加了对作业执行错误的检查
- 改进了错误日志记录
- 提供了更详细的调试信息

## 使用示例

### 基本文本生成（不使用搜索）
```python
result = await windmill_client.generate_text_analysis(
    prompt="请简单分析一下苹果公司的投资价值，控制在100字以内。",
    system_instruction="你是一个专业的金融分析师。",
    search=False  # 不需要搜索
)
```

### 带搜索的文本生成
```python
result = await windmill_client.generate_text_analysis(
    prompt="请分析当前全球股市的整体趋势。",
    system_instruction="你是一个专业的金融分析师。",
    search=True  # 启用搜索功能以获取最新市场信息
)
```

## 影响的文件

### 1. 核心文件
- `financial_analysis/windmill_client.py` - 主要修改文件

### 2. 调用方文件
- `financial_analysis/analysis.py` - 更新了调用方式，启用搜索功能
- `test_windmill_async.py` - 更新了测试用例

### 3. 文档文件
- `docs/windmill_search_parameter_update.md` - 本文档

## 测试结果

所有测试用例都已通过：
- ✅ 基本文本生成
- ✅ 作业执行
- ✅ 触发和等待分离
- ✅ 搜索功能
- ✅ 并发作业处理

## 向后兼容性

此次修改不影响现有的异步调用模式，只是改变了参数结构：
- 移除了不再需要的 `response_schema` 参数
- 新增了更直观的 `search` 布尔参数
- 保持了相同的返回值类型和错误处理机制

## 技术细节

### Windmill 响应结构
Windmill + Gemini API 返回的响应具有以下层次结构：
```
result
├── candidates[]
│   └── content
│       └── parts[]
│           └── text (实际的文本内容)
├── grounding_metadata (搜索结果元数据，当启用搜索时)
└── usage_metadata (使用统计信息)
```

### 搜索功能说明
当 `search=True` 时：
- Windmill 会调用 Gemini API 的搜索功能
- 响应中会包含 `grounding_metadata` 字段，包含搜索结果的元数据
- 生成的文本会基于最新的搜索结果

## 后续计划

1. 监控搜索功能的使用效果
2. 根据实际使用情况优化搜索参数
3. 考虑添加更多搜索相关的配置选项
