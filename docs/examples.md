# 使用示例

## 概述

本文档提供了金融证券分析项目的详细使用示例，包括命令行使用、Python API使用和各种场景的应用案例。

## 快速开始

### 1. 安装和配置

```bash
# 安装依赖
pip install -r requirements.txt

# 复制配置文件
cp .env.example .env

# 编辑配置文件，填入API密钥
vim .env
```

### 2. 基本使用

```bash
# 分析单个股票
financial-analysis analyze 000001

# 分析美股
financial-analysis analyze AAPL --exchange NASDAQ

# 输出JSON格式
financial-analysis analyze 000001 --output json
```

## 命令行使用示例

### 单股票分析

#### 基本分析
```bash
$ financial-analysis analyze 000001
正在分析股票 000001...
============================================================
股票分析报告 - 平安银行 (000001)
============================================================

【基本信息】
股票名称: 平安银行
股票代码: 000001
交易所: SZSE
所属行业: 银行业
市值: ¥1500.00亿

【价格信息】
当前价格: 12.65 CNY
价格变化: +0.15 (+1.20%)

【技术指标】
5日均线: 12.50
20日均线: 12.30
RSI: 65.50
MACD: 0.15

【趋势分析】
短期趋势：上升；价格位于5日均线上方；价格位于20日均线上方；RSI处于正常区间

【新闻分析】
新闻数量: 5 条
整体情感: 正面

新闻摘要: 平安银行近期发布三季度财报，业绩超出市场预期...

【投资建议】
综合评级: 买入
风险等级: 中
投资建议: 综合评分: 4分；技术面和基本面均显示积极信号，建议适量买入

分析时间: 2024-01-15 10:30:00
============================================================
```

#### 详细分析
```bash
$ financial-analysis analyze 000001 --verbose
# 包含更多技术指标、新闻详情和AI分析
```

#### JSON输出
```bash
$ financial-analysis analyze 000001 --output json --file report.json
正在分析股票 000001...
分析报告已保存到: report.json
```

### 批量分析

#### 创建股票列表文件
```bash
$ cat > stocks.txt << EOF
000001
000002
600036
AAPL:NASDAQ
TSLA:NASDAQ
EOF
```

#### 批量分析
```bash
$ financial-analysis batch stocks.txt
开始批量分析 5 只股票...
[1/5] 分析 000001...
  ✓ 平安银行 - 买入
[2/5] 分析 000002...
  ✓ 万科A - 持有
[3/5] 分析 600036...
  ✓ 招商银行 - 买入
[4/5] 分析 AAPL:NASDAQ...
  ✓ Apple Inc. - 持有
[5/5] 分析 TSLA:NASDAQ...
  ✓ Tesla, Inc. - 卖出

================================================================================
批量股票分析报告 - 共 5 只股票
================================================================================

【汇总统计】
买入推荐: 2 只
持有推荐: 2 只
卖出推荐: 1 只

【详细列表】
序号 代码     名称         当前价格   涨跌幅   评级   风险
----------------------------------------------------------------------
1    000001   平安银行     12.65      +1.20%   买入   中  
2    000002   万科A        8.50       -0.50%   持有   中  
3    600036   招商银行     35.20      +2.10%   买入   低  
4    AAPL     Apple Inc.   150.25     +0.80%   持有   低  
5    TSLA     Tesla, Inc.  220.50     -3.20%   卖出   高  

分析时间: 2024-01-15 10:30:00
================================================================================
```

#### 批量分析输出到目录
```bash
$ financial-analysis batch stocks.txt --output json --output-dir reports/
批量分析报告已保存到: reports/batch_analysis_20240115_103000.json
```

### 交互模式

```bash
$ financial-analysis interactive
欢迎使用金融证券分析工具 - 交互模式
输入股票代码进行分析，输入 'quit' 或 'exit' 退出
格式: 股票代码[:交易所] (例如: 000001:SZSE 或 AAPL)
--------------------------------------------------

请输入股票代码: 000001
正在分析 000001...
[分析结果显示]

请输入股票代码: AAPL
正在分析 AAPL...
[分析结果显示]

请输入股票代码: quit
再见!
```

## Python API使用示例

### 基本API使用

```python
from financial_analysis import AnalysisEngine

# 创建分析引擎
engine = AnalysisEngine()

# 分析单个股票
report = engine.generate_analysis_report("000001", "SZSE")

if report:
    print(f"股票名称: {report.stock_info.name}")
    print(f"当前价格: {report.current_price}")
    print(f"综合评级: {report.overall_rating}")
    print(f"投资建议: {report.investment_advice}")
else:
    print("分析失败")
```

### 分模块使用

```python
from financial_analysis import StockDataProvider, NewsSearcher

# 股票数据获取
data_provider = StockDataProvider()

# 获取股票基本信息
stock_info = data_provider.get_stock_info("000001", "SZSE")
print(f"股票名称: {stock_info.name}")
print(f"所属行业: {stock_info.sector}")

# 获取价格数据
prices = data_provider.get_stock_prices("000001", days=30)
print(f"获取到 {len(prices)} 天的价格数据")
print(f"最新收盘价: {prices[-1].close_price}")

# 获取技术指标
indicators = data_provider.get_technical_indicators("000001")
print(f"RSI: {indicators.rsi}")
print(f"MACD: {indicators.macd}")

# 新闻搜索
news_searcher = NewsSearcher()
news_items = news_searcher.search_stock_news(stock_info, days=7)
print(f"找到 {len(news_items)} 条相关新闻")

# 分析新闻情感
sentiment_result = news_searcher.analyze_news_sentiment(news_items)
print(f"新闻整体情感: {sentiment_result['overall_sentiment']}")
print(f"正面新闻: {sentiment_result['positive_count']} 条")
print(f"负面新闻: {sentiment_result['negative_count']} 条")

# 生成新闻摘要
summary = news_searcher.generate_news_summary(news_items, stock_info)
print(f"新闻摘要: {summary}")
```

### 批量分析

```python
from financial_analysis import AnalysisEngine
import json

# 股票列表
stocks = [
    ("000001", "SZSE"),
    ("000002", "SZSE"),
    ("600036", "SSE"),
    ("AAPL", "NASDAQ"),
    ("TSLA", "NASDAQ")
]

engine = AnalysisEngine()
results = []

for symbol, exchange in stocks:
    print(f"分析 {symbol}...")
    report = engine.generate_analysis_report(symbol, exchange)
    
    if report:
        result = {
            "symbol": symbol,
            "name": report.stock_info.name,
            "current_price": report.current_price,
            "price_change_percent": report.price_change_percent,
            "overall_rating": report.overall_rating,
            "risk_level": report.risk_level
        }
        results.append(result)
        print(f"  ✓ {report.stock_info.name} - {report.overall_rating}")
    else:
        print(f"  ✗ 分析失败")

# 保存结果
with open("batch_results.json", "w", encoding="utf-8") as f:
    json.dump(results, f, ensure_ascii=False, indent=2)

print(f"批量分析完成，共分析 {len(results)} 只股票")
```

### 自定义分析逻辑

```python
from financial_analysis import StockDataProvider, NewsSearcher
from datetime import datetime, timedelta

def custom_analysis(symbol, exchange=None):
    """自定义分析函数"""
    
    # 获取数据
    data_provider = StockDataProvider()
    news_searcher = NewsSearcher()
    
    # 股票信息
    stock_info = data_provider.get_stock_info(symbol, exchange)
    if not stock_info:
        return None
    
    # 价格数据（最近60天）
    prices = data_provider.get_stock_prices(symbol, days=60, exchange=exchange)
    if len(prices) < 20:
        return None
    
    # 技术指标
    indicators = data_provider.get_technical_indicators(symbol, exchange)
    
    # 新闻数据
    news_items = news_searcher.search_stock_news(stock_info, days=14)
    sentiment_result = news_searcher.analyze_news_sentiment(news_items)
    
    # 自定义分析逻辑
    current_price = prices[-1].close_price
    ma20 = indicators.ma20 if indicators else None
    rsi = indicators.rsi if indicators else None
    
    # 简单的买卖信号
    signals = []
    
    if ma20 and current_price > ma20:
        signals.append("价格突破20日均线")
    
    if rsi and rsi < 30:
        signals.append("RSI超卖信号")
    elif rsi and rsi > 70:
        signals.append("RSI超买信号")
    
    if sentiment_result['overall_sentiment'] == 'positive':
        signals.append("新闻面偏向正面")
    elif sentiment_result['overall_sentiment'] == 'negative':
        signals.append("新闻面偏向负面")
    
    return {
        "stock_info": stock_info,
        "current_price": current_price,
        "signals": signals,
        "news_sentiment": sentiment_result['overall_sentiment'],
        "news_count": len(news_items)
    }

# 使用自定义分析
result = custom_analysis("000001", "SZSE")
if result:
    print(f"股票: {result['stock_info'].name}")
    print(f"当前价格: {result['current_price']}")
    print(f"信号: {', '.join(result['signals'])}")
    print(f"新闻情感: {result['news_sentiment']}")
```

## 高级用法

### 配置自定义参数

```python
import os
from financial_analysis.config import settings

# 临时修改配置
original_days = settings.analysis_days
settings.analysis_days = 60  # 分析60天数据

# 使用修改后的配置进行分析
engine = AnalysisEngine()
report = engine.generate_analysis_report("000001")

# 恢复原配置
settings.analysis_days = original_days
```

### 错误处理和重试

```python
import time
from financial_analysis import AnalysisEngine

def robust_analysis(symbol, max_retries=3):
    """带重试机制的分析"""
    engine = AnalysisEngine()
    
    for attempt in range(max_retries):
        try:
            report = engine.generate_analysis_report(symbol)
            if report:
                return report
            else:
                print(f"第 {attempt + 1} 次尝试失败，等待重试...")
                time.sleep(2)
        except Exception as e:
            print(f"第 {attempt + 1} 次尝试出错: {e}")
            if attempt < max_retries - 1:
                time.sleep(5)
    
    return None

# 使用
report = robust_analysis("000001")
if report:
    print("分析成功")
else:
    print("多次尝试后仍然失败")
```

### 数据导出

```python
import pandas as pd
from financial_analysis import AnalysisEngine

def export_to_excel(symbols, filename):
    """导出分析结果到Excel"""
    engine = AnalysisEngine()
    data = []
    
    for symbol in symbols:
        report = engine.generate_analysis_report(symbol)
        if report:
            data.append({
                "股票代码": report.symbol,
                "股票名称": report.stock_info.name,
                "当前价格": report.current_price,
                "涨跌幅": f"{report.price_change_percent:.2f}%",
                "综合评级": report.overall_rating,
                "风险等级": report.risk_level,
                "RSI": report.technical_indicators.rsi,
                "新闻情感": report.news_sentiment,
                "分析时间": report.analysis_date.strftime("%Y-%m-%d %H:%M")
            })
    
    df = pd.DataFrame(data)
    df.to_excel(filename, index=False, engine='openpyxl')
    print(f"数据已导出到 {filename}")

# 使用
symbols = ["000001", "000002", "600036"]
export_to_excel(symbols, "stock_analysis.xlsx")
```

## 注意事项

1. **API限制**: 注意数据源的API调用频率限制
2. **网络连接**: 确保网络连接稳定，某些数据源可能需要代理
3. **配置文件**: 正确配置API密钥和其他参数
4. **数据时效**: 分析结果基于当前数据，具有时效性
5. **投资风险**: 分析结果仅供参考，不构成投资建议

## 故障排除

### 常见问题

1. **无法获取数据**: 检查网络连接和API配置
2. **分析失败**: 查看日志文件了解详细错误信息
3. **性能问题**: 适当调整缓存时间和并发数量
4. **内存占用**: 批量分析时注意内存使用情况

### 日志查看

```bash
# 查看日志文件
tail -f logs/financial_analysis.log

# 设置调试级别
export LOG_LEVEL=DEBUG
financial-analysis analyze 000001 --verbose
```
