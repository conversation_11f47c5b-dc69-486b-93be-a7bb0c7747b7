# 动态数据源管理功能说明

## 概述

动态数据源管理功能是对原有热点新闻收集系统的重大升级，提供了统一的数据源管理框架，支持动态添加、删除、更新数据源，并通过适配器模式实现了不同类型数据源的统一处理。

## 主要特性

- 🔧 **动态管理**: 支持运行时动态添加、删除、更新数据源
- 🔄 **统一格式**: 通过`UnifiedNewsData`模型提供统一的数据结构
- 🎯 **适配器模式**: 支持RSS、API、Web爬虫等多种数据源类型
- 📊 **状态监控**: 实时监控数据源状态和性能指标
- 🛠️ **配置验证**: 自动验证数据源配置的有效性
- 🔌 **插件化**: 支持自定义适配器和预定义数据源
- 📈 **统计分析**: 提供详细的数据源统计和分析功能

## 核心组件

### 1. 数据源适配器 (DataSourceAdapter)

适配器是数据源管理的核心，负责从不同类型的数据源获取数据并转换为统一格式。

#### 基础适配器类型

- **RSSAdapter**: RSS订阅源适配器
- **APIAdapter**: RESTful API适配器  
- **WebAdapter**: 网页爬虫适配器
- **CustomAdapter**: 自定义处理函数适配器

#### 预定义适配器

- **TencentFinanceAdapter**: 腾讯财经API适配器
- **XueqiuAdapter**: 雪球财经API适配器
- **BaiduHotAdapter**: 百度热搜API适配器
- **WeiboHotAdapter**: 微博热搜API适配器

### 2. 数据源注册器 (DataSourceRegistry)

负责数据源的注册、管理和配置存储。

```python
from financial_analysis.data_source_registry import data_source_registry

# 注册新数据源
config = DataSourceConfig(
    source_id="my_rss_source",
    name="我的RSS源",
    source_type="rss",
    adapter_class="RSSAdapter",
    config={"url": "https://example.com/rss.xml"}
)
data_source_registry.register_source(config)

# 列出所有数据源
sources = data_source_registry.list_sources()
```

### 3. 数据源管理API (DataSourceAPI)

提供RESTful风格的数据源管理接口。

```python
from financial_analysis.data_source_api import data_source_api

# 创建数据源
result = data_source_api.create_source({
    "source_id": "test_api",
    "name": "测试API",
    "source_type": "api",
    "adapter_class": "APIAdapter",
    "config": {"url": "https://api.example.com/news"}
})

# 测试数据源连接
test_result = data_source_api.test_source("test_api")
```

### 4. 增强的热点新闻收集器 (HotNewsCollector)

集成了新的数据源管理功能，同时保持向后兼容。

```python
from financial_analysis.hot_news_collector import HotNewsCollector

# 使用新系统
collector = HotNewsCollector(use_new_system=True)

# 收集统一格式的新闻数据
unified_news = collector.collect_unified_news()

# 动态添加数据源
collector.add_data_source({
    "source_id": "dynamic_source",
    "name": "动态数据源",
    "source_type": "rss",
    "adapter_class": "RSSAdapter",
    "config": {"url": "https://example.com/feed.xml"}
})
```

## 数据模型

### DataSourceConfig - 数据源配置

```python
class DataSourceConfig(BaseModel):
    source_id: str              # 数据源唯一标识
    name: str                   # 数据源名称
    source_type: str            # 数据源类型：rss/api/web/custom
    adapter_class: str          # 适配器类名
    config: Dict[str, Any]      # 数据源特定配置
    enabled: bool = True        # 是否启用
    priority: int = 1           # 优先级
    fetch_interval: int = 300   # 获取间隔（秒）
    retry_count: int = 3        # 重试次数
    timeout: int = 30           # 超时时间（秒）
```

### UnifiedNewsData - 统一新闻数据

```python
class UnifiedNewsData(BaseModel):
    id: str                     # 新闻唯一标识
    title: str                  # 新闻标题
    content: Optional[str]      # 新闻内容
    source_id: str              # 数据源ID
    source_name: str            # 数据源名称
    original_url: Optional[str] # 原始链接
    publish_time: Optional[datetime] # 发布时间
    fetch_time: datetime        # 获取时间
    category: Optional[str]     # 新闻分类
    keywords: List[str]         # 关键词列表
    # ... 更多字段
```

## 使用指南

### 1. 添加RSS数据源

```python
from financial_analysis.data_source_api import data_source_api

rss_source = {
    "source_id": "my_finance_rss",
    "name": "我的财经RSS",
    "source_type": "rss",
    "adapter_class": "RSSAdapter",
    "config": {
        "url": "https://finance.example.com/rss.xml"
    },
    "enabled": True,
    "priority": 1,
    "fetch_interval": 300
}

result = data_source_api.create_source(rss_source)
if result["success"]:
    print(f"成功添加RSS数据源: {result['source_id']}")
```

### 2. 添加API数据源

```python
api_source = {
    "source_id": "news_api",
    "name": "新闻API",
    "source_type": "api",
    "adapter_class": "APIAdapter",
    "config": {
        "url": "https://newsapi.org/v2/top-headlines",
        "headers": {
            "X-API-Key": "your_api_key"
        },
        "params": {
            "country": "us",
            "category": "business"
        },
        "field_mapping": {
            "title": ["title"],
            "content": ["description"],
            "url": ["url"],
            "publish_time": ["publishedAt"]
        },
        "data_path": ["articles"]
    }
}

result = data_source_api.create_source(api_source)
```

### 3. 添加Web爬虫数据源

```python
web_source = {
    "source_id": "finance_web",
    "name": "财经网站",
    "source_type": "web",
    "adapter_class": "WebAdapter",
    "config": {
        "url": "https://finance.example.com/news",
        "selectors": {
            "container": ".news-item",
            "title": "h3.title",
            "content": ".summary",
            "link": "a",
            "time": ".publish-time"
        }
    }
}

result = data_source_api.create_source(web_source)
```

### 4. 使用自定义处理函数

```python
# 首先定义自定义处理函数
def my_custom_handler(config):
    """自定义数据处理函数"""
    # 实现自定义的数据获取逻辑
    raw_data = []  # 从某个数据源获取原始数据
    
    # 返回标准格式的数据列表
    return [
        {
            "title": "自定义新闻标题",
            "content": "自定义新闻内容",
            "url": "https://example.com/news1",
            "publish_time": "2023-12-01T10:00:00Z"
        }
    ]

# 注册自定义数据源
custom_source = {
    "source_id": "custom_handler",
    "name": "自定义处理器",
    "source_type": "custom",
    "adapter_class": "CustomAdapter",
    "config": {
        "handler_function": "my_module.my_custom_handler"
    }
}

result = data_source_api.create_source(custom_source)
```

### 5. 数据源管理操作

```python
# 列出所有数据源
sources = data_source_api.list_sources()

# 获取特定数据源信息
source_info = data_source_api.get_source("my_finance_rss")

# 更新数据源配置
data_source_api.update_source("my_finance_rss", {
    "fetch_interval": 600,  # 改为10分钟
    "enabled": True
})

# 测试数据源连接
test_result = data_source_api.test_source("my_finance_rss")

# 启用/禁用数据源
data_source_api.enable_source("my_finance_rss")
data_source_api.disable_source("my_finance_rss")

# 删除数据源
data_source_api.delete_source("my_finance_rss")
```

### 6. 数据收集和处理

```python
from financial_analysis.hot_news_collector import HotNewsCollector

# 创建收集器实例
collector = HotNewsCollector(use_new_system=True)

# 收集统一格式的新闻数据
unified_news = collector.collect_unified_news(force_refresh=True)

# 收集传统格式的新闻数据（向后兼容）
hot_news = collector.collect_hot_news(force_refresh=True)

# 从所有数据源收集数据
all_data = data_source_api.collect_all_sources(force_refresh=True)
```

## 配置文件

数据源配置可以通过JSON文件进行管理：

```json
[
  {
    "source_id": "sina_finance_rss",
    "name": "新浪财经RSS",
    "source_type": "rss",
    "adapter_class": "RSSAdapter",
    "config": {
      "url": "https://feed.mix.sina.com.cn/api/roll/get?pageid=153&lid=1686&k=&num=50&page=1"
    },
    "enabled": true,
    "priority": 1,
    "fetch_interval": 300
  },
  {
    "source_id": "custom_api",
    "name": "自定义API",
    "source_type": "api",
    "adapter_class": "APIAdapter",
    "config": {
      "url": "https://api.example.com/news",
      "headers": {
        "Authorization": "Bearer your_token"
      },
      "params": {
        "limit": 50
      }
    },
    "enabled": true,
    "priority": 2,
    "fetch_interval": 600
  }
]
```

## 监控和统计

```python
# 获取数据源统计信息
stats = data_source_api.get_statistics()
print(f"总数据源: {stats['data']['total_sources']}")
print(f"启用数据源: {stats['data']['enabled_sources']}")

# 获取数据源状态
status = data_source_api.get_source_status("my_finance_rss")
print(f"状态: {status['status']}")
print(f"成功次数: {status['success_count']}")
print(f"错误次数: {status['error_count']}")
```

## 最佳实践

1. **合理设置获取间隔**: 根据数据源的更新频率设置合适的`fetch_interval`
2. **监控数据源状态**: 定期检查数据源的健康状态和错误信息
3. **配置重试机制**: 为不稳定的数据源设置合适的重试次数
4. **使用优先级**: 通过`priority`字段控制数据源的处理顺序
5. **测试配置**: 在启用新数据源前先进行连接测试
6. **备份配置**: 定期备份数据源配置文件

## 故障排除

### 常见问题

1. **数据源连接失败**
   - 检查URL是否正确
   - 验证API密钥是否有效
   - 确认网络连接正常

2. **数据解析错误**
   - 检查字段映射配置
   - 验证选择器是否正确
   - 查看原始数据格式

3. **性能问题**
   - 调整获取间隔
   - 减少并发数据源数量
   - 优化数据处理逻辑

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger('financial_analysis').setLevel(logging.DEBUG)

# 测试单个数据源
test_result = data_source_api.test_source("problematic_source")
print(test_result)

# 检查适配器状态
adapter = data_source_registry.get_adapter("problematic_source")
if adapter:
    status = adapter.get_status()
    print(f"错误信息: {status.last_error}")
```

## 扩展开发

### 创建自定义适配器

```python
from financial_analysis.data_source_adapters import DataSourceAdapter
from financial_analysis.models import UnifiedNewsData

class MyCustomAdapter(DataSourceAdapter):
    """自定义适配器示例"""
    
    def validate_config(self) -> bool:
        """验证配置"""
        return bool(self.config.config.get('custom_url'))
    
    def fetch_data(self) -> List[UnifiedNewsData]:
        """获取数据"""
        # 实现自定义的数据获取逻辑
        pass

# 注册自定义适配器
from financial_analysis.data_source_adapters import register_adapter
register_adapter('my_custom', MyCustomAdapter)
```

这个动态数据源管理功能为热点新闻收集系统提供了强大的扩展能力，支持各种类型的数据源，并通过统一的接口进行管理。通过合理配置和使用，可以大大提高数据收集的效率和可靠性。
