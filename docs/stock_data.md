# 股票数据获取模块说明

## 概述

`stock_data.py` 模块负责从各种数据源获取股票的基本信息、价格数据和技术指标。支持多个交易所的股票代码，包括上交所、深交所、港交所、纳斯达克等。

## 主要类

### StockDataProvider

股票数据提供者类，提供统一的接口获取股票数据。

#### 主要方法

##### get_stock_info(symbol, exchange=None)

获取股票基本信息

**参数：**
- `symbol` (str): 股票代码
- `exchange` (str, 可选): 交易所代码

**返回：**
- `StockInfo`: 股票基本信息对象，包含股票代码、名称、交易所、货币、行业、市值等信息

**示例：**
```python
provider = StockDataProvider()
info = provider.get_stock_info("000001", "SZSE")
print(f"股票名称: {info.name}")
print(f"所属行业: {info.sector}")
```

##### get_stock_prices(symbol, days=30, exchange=None)

获取股票历史价格数据

**参数：**
- `symbol` (str): 股票代码
- `days` (int): 获取天数，默认30天
- `exchange` (str, 可选): 交易所代码

**返回：**
- `List[StockPrice]`: 股票价格数据列表，包含开盘价、最高价、最低价、收盘价、成交量等

**示例：**
```python
provider = StockDataProvider()
prices = provider.get_stock_prices("000001", days=7)
for price in prices:
    print(f"{price.date}: 收盘价 {price.close_price}")
```

##### get_technical_indicators(symbol, exchange=None)

获取股票技术指标

**参数：**
- `symbol` (str): 股票代码
- `exchange` (str, 可选): 交易所代码

**返回：**
- `TechnicalIndicators`: 技术指标对象，包含MA、RSI、MACD、KDJ等指标

**示例：**
```python
provider = StockDataProvider()
indicators = provider.get_technical_indicators("000001")
print(f"RSI: {indicators.rsi}")
print(f"MACD: {indicators.macd}")
```

## 支持的交易所

- **SSE**: 上海证券交易所（股票代码以6开头）
- **SZSE**: 深圳证券交易所（股票代码以0、3开头）
- **HK**: 香港交易所
- **NASDAQ**: 纳斯达克
- **NYSE**: 纽约证券交易所

## 数据源

- **中国A股**: 使用 akshare 库获取数据
- **国际股票**: 使用 yfinance 库获取数据

## 技术指标说明

### 移动平均线 (MA)
- MA5: 5日移动平均线
- MA10: 10日移动平均线
- MA20: 20日移动平均线
- MA60: 60日移动平均线

### 相对强弱指数 (RSI)
衡量股票超买超卖状态的指标，取值范围0-100
- RSI > 70: 超买状态
- RSI < 30: 超卖状态

### MACD
移动平均收敛发散指标，用于判断趋势变化

### KDJ指标
随机指标，包含K、D、J三个值，用于判断买卖时机

## 缓存机制

模块内置简单的内存缓存机制，避免频繁请求相同数据：
- 缓存时间: 由配置文件中的 `data_cache_duration` 参数控制（默认300秒）
- 缓存键: 基于股票代码和请求类型生成

## 错误处理

- 网络请求失败时会自动重试
- 数据解析失败时返回None或空列表
- 所有异常都会记录到日志中

## 注意事项

1. 使用前需要确保网络连接正常
2. 某些数据源可能有访问频率限制
3. 技术指标计算需要足够的历史数据（至少20个交易日）
4. 不同交易所的股票代码格式可能不同，模块会自动进行标准化处理
