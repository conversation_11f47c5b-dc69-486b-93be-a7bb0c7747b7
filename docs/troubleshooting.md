# 故障排除指南

## 模拟数据问题分析与解决方案

### 问题现象

当运行金融分析项目时，所有的调用参数示例都显示为模拟数据，而不是真实的市场数据。

### 问题根源分析

#### 1. 股票数据模拟的原因

**问题**: akshare包未安装或不可用
- **现象**: 日志显示 "akshare不可用，中国股票数据将使用模拟数据"
- **影响**: 所有中国A股数据（SSE、SZSE交易所）都会使用预设的模拟数据
- **解决方案**: 
  ```bash
  pip install akshare
  ```

**代码逻辑**:
```python
# financial_analysis/stock_data.py
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    ak = None
    AKSHARE_AVAILABLE = False
    logger.warning("akshare不可用，中国股票数据将使用模拟数据")
```

#### 2. 新闻数据模拟的原因

**问题**: Windmill配置不完整或API调用失败
- **现象**: 
  - 日志显示 "Windmill配置不完整，使用模拟数据"
  - 或者 "Windmill新闻搜索失败" 后降级到模拟数据
- **影响**: 新闻搜索、情感分析、摘要生成都使用预设的模拟数据
- **解决方案**: 检查并修复Windmill配置

#### 3. AI分析模拟的原因

**问题**: Windmill权限不足或配置错误
- **现象**: 
  - "missing required scope: run:script/f/gemini/js_structured_output"
  - AI分析降级到简化版本
- **影响**: 无法获得基于大语言模型的深度分析
- **解决方案**: 配置正确的Windmill权限和脚本路径

### 详细解决步骤

#### 步骤1: 安装必要依赖

```bash
# 安装akshare用于中国股票数据
pip install akshare

# 安装其他可能缺失的依赖
pip install yfinance  # 用于国际股票数据
pip install aiohttp   # 用于异步HTTP请求
```

#### 步骤2: 检查配置文件

确保 `.env` 文件包含正确的配置：

```env
# Windmill配置
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your_actual_token_here
WINDMILL_WORKSPACE=my-workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=js_structured_output  # 确保脚本名称正确
```

#### 步骤3: 验证Windmill权限

联系Windmill管理员确保token具有以下权限：
- `run:script/f/gemini/js_structured_output`
- 对应工作空间的访问权限

#### 步骤4: 测试数据源

```python
# 测试股票数据
from financial_analysis import StockDataProvider
provider = StockDataProvider()
info = provider.get_stock_info("000001", "SZSE")
print(f"数据源测试: {info.name if info else '失败'}")

# 测试新闻搜索
from financial_analysis import NewsSearcher
searcher = NewsSearcher()
news = searcher.search_stock_news(info) if info else []
print(f"新闻数据: {'真实' if len(news) > 3 else '模拟'}")
```

### 模拟数据的设计目的

项目中的模拟数据机制是一个**降级保护**设计，确保在以下情况下系统仍能正常运行：

1. **网络问题**: 无法访问外部数据源
2. **API限制**: 达到调用频率限制
3. **配置问题**: 缺少必要的API密钥或配置
4. **依赖问题**: 某些包未正确安装

### 如何识别数据类型

#### 真实数据特征:
- 股票价格会实时变化
- 新闻内容具有时效性和真实性
- 技术指标基于真实市场数据计算
- AI分析内容丰富且具有专业性

#### 模拟数据特征:
- 股票价格相对固定（基于算法生成）
- 新闻内容通用且重复
- 日志中出现"模拟"、"mock"等关键词
- AI分析内容简化

### 监控和调试

#### 启用详细日志:
```env
LOG_LEVEL=DEBUG
```

#### 关键日志信息:
- `akshare不可用` → 股票数据问题
- `Windmill配置不完整` → AI功能问题
- `生成模拟新闻数据` → 新闻数据问题
- `missing required scope` → 权限问题

### 解决方案验证结果

**✅ 股票数据问题已解决**
- 安装akshare后，中国A股数据恢复正常
- 真实价格数据：平安银行 12.27元，苹果 207.57美元
- 技术指标基于真实市场数据计算

**⚠️ Windmill权限问题待解决**
- 错误：`missing required scope: run:script/f/gemini/js_structured_output`
- 需要联系Windmill管理员配置正确权限
- 临时方案：系统会降级到简化分析

### 常见问题FAQ

**Q: 为什么股票数据是真实的，但新闻和AI分析还是模拟的？**
A: 这是因为不同功能使用不同的数据源。股票数据来自akshare/yfinance，而新闻和AI分析需要Windmill权限。

**Q: 如何获得完整的真实数据？**
A: 需要同时满足：
1. 安装akshare包（已完成）
2. 配置有效的Windmill token和权限（待完成）
3. 网络连接正常

**Q: 模拟数据的准确性如何？**
A: 模拟数据仅用于功能演示和测试，不应用于实际投资决策。

### 性能优化建议

1. **缓存机制**: 启用数据缓存减少API调用
2. **批量请求**: 对于多股票分析，使用批量接口
3. **错误重试**: 配置合理的重试机制
4. **监控告警**: 设置数据质量监控

---

*最后更新: 2025-08-01*
