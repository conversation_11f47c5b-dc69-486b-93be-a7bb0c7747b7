# 历史消息检测功能

## 概述

历史消息检测功能使用 Windmill 的 `text_generation` 接口的搜索工具来判断获取的新消息是否为历史消息。通过配置 `search=True`，让大模型根据搜索结果返回每个消息的最初发布时间，从而准确判断消息的时效性。

## 核心特性

- 🔍 **智能搜索**: 使用 Gemini 大模型的搜索功能查找消息的最初发布时间
- 📅 **时间判断**: 基于配置的天数阈值判断消息是否为历史消息
- 📊 **批量处理**: 支持批量检查多条消息的历史性
- 🎯 **准确识别**: 区分原创消息和转载消息，识别真正的发布时间
- 📈 **统计分析**: 提供详细的检查结果统计信息

## 工作原理

### 1. 搜索提示词构造

系统会为每条消息构造包含以下信息的搜索提示词：
- 消息标题和内容
- 声明的发布时间
- 消息来源
- 当前日期和判断标准

### 2. 调用搜索接口

```python
# 调用 text_generation 接口
url = f"{windmill_base_url}/api/w/my-workspace/jobs/run/p/f/gemini/text_generation"
payload = {
    "prompt": search_prompt,
    "search": True  # 启用搜索功能
}
```

### 3. 结果解析

系统解析搜索结果，提取每条消息的：
- 是否为历史消息的判断
- 最初发布时间
- 判断依据

### 4. 消息更新

根据搜索结果更新消息的 `is_historical` 字段和 `publish_time` 字段。

## 使用方法

### 1. 使用分析器直接检查

```python
from financial_analysis.hot_news_analyzer import HotNewsAnalyzer

analyzer = HotNewsAnalyzer()
checked_news = analyzer.check_historical_news_with_search(news_items)

# 过滤历史消息
recent_news = [news for news in checked_news if not news.is_historical]
```

### 2. 使用管理器检查

```python
from financial_analysis.hot_news_manager import hot_news_manager

# 检查历史消息并获取详细统计
result = hot_news_manager.check_historical_messages_with_search(news_items)

print(f"历史消息: {result['historical_count']} 条")
print(f"最近消息: {result['recent_count']} 条")
```

### 3. 直接获取最近消息

```python
# 一步到位获取最近消息
recent_messages = hot_news_manager.get_recent_messages_only(news_items)
```

## 配置参数

在 `.env` 文件中配置相关参数：

```env
# Windmill配置
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your_windmill_token
WINDMILL_WORKSPACE=my-workspace

# 历史消息判断配置
HOT_NEWS_HISTORY_CHECK_DAYS=3  # 历史消息检查天数
```

## 搜索提示词示例

```
请帮我检查以下新闻列表中的每条新闻是否为历史消息。我需要你使用搜索工具来查找这些新闻的最初发布时间，以判断它们是否是3天前就已经发布过的历史信息。

当前日期: 2024-01-20
历史消息判断标准: 如果新闻的最初发布时间距离当前日期超过3天，则认为是历史消息。

新闻列表:
1. 标题: 苹果公司发布iPhone 15系列手机
   内容: 苹果公司在秋季发布会上正式发布了iPhone 15系列手机...
   来源: 科技日报
   声明的发布时间: 2024-01-19 10:30:00
   获取时间: 2024-01-20 09:15:00

请使用搜索工具查找每条新闻的相关信息，并按以下格式返回结果：

格式要求:
1. 新闻编号: [编号]
2. 是否为历史消息: [是/否]
3. 最初发布时间: [YYYY-MM-DD HH:MM:SS 或 "未找到"]
4. 判断依据: [简要说明搜索到的相关信息]
```

## 返回结果格式

搜索工具返回的结果格式：

```
1. 新闻编号: 1
2. 是否为历史消息: 否
3. 最初发布时间: 2024-01-19 10:30:00
4. 判断依据: 搜索结果显示该新闻确实是2024年1月19日首次发布，属于最近消息

2. 新闻编号: 2
3. 是否为历史消息: 是
4. 最初发布时间: 2024-01-15 14:20:00
5. 判断依据: 搜索发现该新闻最初发布于2024年1月15日，距今超过3天，属于历史消息
```

## 集成到处理流程

新的处理流程：

```python
def collect_and_process_hot_news(self, force_refresh: bool = False):
    # 1. 收集热点信息
    raw_news = self.collector.collect_hot_news(force_refresh)
    
    # 2. 使用搜索工具检查历史消息
    search_checked_news = self.analyzer.check_historical_news_with_search(raw_news)
    
    # 3. 分析非历史消息
    non_historical_news = [news for news in search_checked_news if not news.is_historical]
    analyzed_news = self.analyzer.analyze_news_batch(non_historical_news)
    
    # 4. 后续处理...
```

## 优势

### 1. 准确性提升
- 基于实际搜索结果判断，而不仅仅依赖声明的发布时间
- 能够识别转载和重复发布的内容
- 区分原创内容和历史内容的再次传播

### 2. 智能化程度高
- 利用大模型的理解能力和搜索能力
- 能够处理复杂的时间表述和内容变体
- 自动识别突发事件的后续报道

### 3. 批量处理效率
- 一次请求可以处理多条消息
- 减少API调用次数
- 提高整体处理效率

### 4. 可配置性强
- 支持自定义历史消息判断天数
- 可以调整搜索策略和提示词
- 灵活适应不同的业务需求

## 注意事项

1. **网络依赖**: 功能依赖于 Windmill 服务的可用性
2. **处理时间**: 搜索功能可能需要较长时间，建议设置合适的超时时间
3. **成本考虑**: 搜索功能可能产生额外的API调用成本
4. **准确性限制**: 搜索结果的准确性依赖于网络上可获取的信息

## 测试

使用提供的测试脚本验证功能：

```bash
python test_historical_search.py
```

测试脚本会创建模拟数据并测试各种场景，包括：
- 直接使用分析器检查
- 使用管理器检查并获取统计信息
- 获取过滤后的最近消息列表

## 未来改进

1. **缓存机制**: 对搜索结果进行缓存，避免重复搜索
2. **多源验证**: 结合多个搜索源提高准确性
3. **学习优化**: 基于历史检查结果优化搜索策略
4. **实时更新**: 支持实时更新消息的历史状态
