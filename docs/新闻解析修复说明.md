# 新闻解析修复说明

## 问题描述

在执行 `financial-analysis analyze 000001` 命令时，虽然能够查询到平安银行(000001)的最新消息，但是没有正确解析出其中的JSON数据。API返回的响应包含了新闻数据，但解析逻辑无法正确提取。

## 问题原因

### 1. 响应数据结构不匹配

API返回的数据结构如下：
```json
{
  "index": 0,
  "content": {
    "role": "model",
    "parts": [
      {
        "text": "根据您提供的时间范围（2025-07-25 至 2025-08-01），以下是关于平安银行（股票代码：000001）的相关新闻分析：\n\n..."
      },
      {
        "text": "```json\n{\"news_items\": [...]}```"
      }
    ]
  }
}
```

原有的解析逻辑只检查了 `parts[0]`，而JSON数据实际在 `parts[1]` 中，并且还包含了markdown代码块标记。

### 2. 缓存机制问题

NewsSearcher使用实例级别的缓存，不同实例之间缓存不共享，导致测试时清除的缓存对分析引擎中的实例无效。

## 解决方案

### 1. 改进JSON解析逻辑

在 `financial_analysis/news_search.py` 中：

#### a) 遍历所有parts
修改了解析逻辑，遍历响应中的所有 `parts`，而不是只检查第一个：

```python
# 遍历所有parts，寻找包含JSON数据的部分
for part in parts:
    if 'text' in part:
        text_content = part['text']
        logger.debug(f"检查part文本内容: {text_content[:200]}...")
        
        # 尝试提取JSON数据
        json_data = self._extract_json_from_text(text_content)
        if json_data:
            if isinstance(json_data, dict) and 'news_items' in json_data:
                news_data = json_data['news_items']
                logger.info(f"成功从part中解析到 {len(news_data)} 条新闻数据")
                break
```

#### b) 新增智能JSON提取方法
添加了 `_extract_json_from_text` 方法，支持多种JSON格式：

1. **纯JSON字符串**：直接解析整个文本
2. **Markdown代码块**：提取 ````json ... ```` 或 ```` ... ``` 格式
3. **混合内容**：从包含其他文本的内容中提取JSON对象
4. **智能括号匹配**：使用括号计数算法提取完整的JSON结构

```python
def _extract_json_from_text(self, text_content: str) -> Optional[Dict[str, Any]]:
    """
    从文本内容中提取JSON数据
    
    支持以下格式：
    1. 纯JSON字符串
    2. Markdown代码块包装的JSON (```json ... ```)
    3. 包含其他文本的混合内容中的JSON
    """
    # 实现多种解析策略...
```

### 2. 修复缓存共享问题

将实例级别的缓存改为类级别的全局缓存：

```python
class NewsSearcher:
    """新闻搜索器类"""
    
    # 类级别的缓存，所有实例共享
    _global_cache = {}

    def __init__(self):
        """初始化新闻搜索器"""
        self._cache = NewsSearcher._global_cache  # 使用全局缓存
        self._cache_timeout = settings.data_cache_duration
        logger.info("新闻搜索器初始化完成")
    
    @classmethod
    def clear_global_cache(cls):
        """清除全局缓存"""
        cls._global_cache.clear()
        logger.info("新闻搜索器全局缓存已清除")
```

## 修复效果

修复后，`financial-analysis analyze 000001` 命令能够：

1. **成功解析新闻数据**：从API响应中正确提取JSON格式的新闻信息
2. **获取完整新闻列表**：成功获取到10条相关新闻
3. **正确分析情感倾向**：将新闻情感从"中性"正确识别为"正面"
4. **生成准确摘要**：基于实际新闻内容生成有意义的摘要

### 修复前
```
【新闻分析】
新闻数量: 0 条
整体情感: 中性
新闻摘要: 未找到关于 平安银行 (000001) 的相关新闻。
```

### 修复后
```
【新闻分析】
新闻数量: 10 条
整体情感: 正面
新闻摘要: 平安银行近期表现活跃，在资金面上，7月24日、25日连续出现主力资金净流入，市场分析认为其低估值和高股息有望支撑股价走势...
```

## 技术要点

1. **多层级数据结构解析**：能够处理复杂的嵌套响应结构
2. **多格式JSON提取**：支持各种JSON包装格式的智能识别
3. **全局缓存管理**：确保不同实例间的缓存一致性
4. **详细调试日志**：提供完整的解析过程跟踪

## 测试验证

可以通过以下命令验证修复效果：

```bash
# 清除缓存
python -c "from financial_analysis.news_search import NewsSearcher; NewsSearcher.clear_global_cache()"

# 运行分析
financial-analysis analyze 000001
```

修复后的系统能够稳定地解析和处理各种格式的API响应，显著提升了新闻数据获取的可靠性。
