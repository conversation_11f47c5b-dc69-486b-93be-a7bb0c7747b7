# 金融证券分析项目

基于人工智能技术的金融证券分析工具，利用大语言模型进行股票数据分析、新闻情感分析和智能化投资建议生成。

## 项目特性

- 🚀 **多数据源支持**: 支持中国A股、美股、港股等多个交易所
- 🤖 **AI驱动分析**: 集成Gemini大语言模型进行智能分析
- 📊 **技术指标计算**: 自动计算MA、RSI、MACD、KDJ等技术指标
- 📰 **新闻情感分析**: 实时获取并分析股票相关新闻的情感倾向
- 📈 **综合评级系统**: 基于技术面、基本面和新闻面的综合投资建议
- 🔧 **灵活的接口**: 提供Python API和命令行工具
- 📝 **详细报告**: 生成包含AI分析的详细投资报告

## 快速开始

### 1. 安装依赖

```bash
# 克隆项目
git clone https://git.atjog.com/aier/financial-analysis.git
cd financial-analysis

# 安装依赖
pip install -r requirements.txt

# 或使用开发模式安装
pip install -e .
```

### 2. 配置环境

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件，填入必要的API密钥
vim .env
```

配置示例：
```env
# Gemini API配置
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro

# Windmill配置（可选，用于AI分析）
WINDMILL_BASE_URL=your_windmill_base_url
WINDMILL_TOKEN=your_windmill_token

# 其他配置
LOG_LEVEL=INFO
ANALYSIS_DAYS=30
NEWS_SEARCH_DAYS=7
```

### 3. 运行演示

```bash
# 运行演示脚本
python demo.py

# 或使用命令行工具
financial-analysis analyze 000001
```

## 使用方法

### 命令行使用

```bash
# 分析单个股票
financial-analysis analyze 000001              # 中国A股
financial-analysis analyze AAPL --exchange NASDAQ  # 美股

# 输出JSON格式
financial-analysis analyze 000001 --output json --file report.json

# 批量分析
echo -e "000001\n000002\nAAPL:NASDAQ" > stocks.txt
financial-analysis batch stocks.txt

# 交互模式
financial-analysis interactive
```

### Python API使用

```python
from financial_analysis import AnalysisEngine

# 创建分析引擎
engine = AnalysisEngine()

# 生成分析报告
report = engine.generate_analysis_report("000001", "SZSE")

if report:
    print(f"股票名称: {report.stock_info.name}")
    print(f"当前价格: {report.current_price}")
    print(f"综合评级: {report.overall_rating}")
    print(f"投资建议: {report.investment_advice}")
```

### 分模块使用

```python
from financial_analysis import StockDataProvider, NewsSearcher

# 股票数据获取
data_provider = StockDataProvider()
stock_info = data_provider.get_stock_info("000001", "SZSE")
prices = data_provider.get_stock_prices("000001", days=30)
indicators = data_provider.get_technical_indicators("000001")

# 新闻搜索和分析
news_searcher = NewsSearcher()
news_items = news_searcher.search_stock_news(stock_info, days=7)
sentiment = news_searcher.analyze_news_sentiment(news_items)
summary = news_searcher.generate_news_summary(news_items, stock_info)
```

## 项目结构

```
financial-analysis/
├── financial_analysis/          # 主要代码
│   ├── __init__.py
│   ├── config.py               # 配置管理
│   ├── models.py               # 数据模型
│   ├── utils.py                # 工具函数
│   ├── stock_data.py           # 股票数据获取
│   ├── news_search.py          # 新闻搜索
│   ├── analysis.py             # 分析引擎
│   └── main.py                 # 主程序入口
├── docs/                       # 文档
│   ├── README.md
│   ├── config.md
│   ├── models.md
│   ├── stock_data.md
│   ├── news_search.md
│   ├── analysis.md
│   ├── api.md
│   └── examples.md
├── tests/                      # 测试代码
│   ├── conftest.py
│   ├── test_models.py
│   ├── test_utils.py
│   ├── test_stock_data.py
│   └── test_news_search.py
├── requirements.txt            # 依赖列表
├── setup.py                   # 安装配置
├── pytest.ini                # 测试配置
├── demo.py                    # 演示脚本
└── .env.example               # 配置示例
```

## 支持的交易所

- **SSE** (上海证券交易所): 股票代码以6开头
- **SZSE** (深圳证券交易所): 股票代码以0、3开头
- **HK** (香港交易所): 港股
- **NASDAQ** (纳斯达克): 美股科技股
- **NYSE** (纽约证券交易所): 美股蓝筹股

## 技术栈

- **Python 3.8+**: 主要开发语言
- **pandas/numpy**: 数据处理和计算
- **yfinance**: 国际股票数据获取
- **akshare**: 中国股票数据获取
- **pydantic**: 数据验证和序列化
- **loguru**: 日志管理
- **requests**: HTTP请求
- **pytest**: 单元测试

## 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_models.py

# 生成覆盖率报告
pytest --cov=financial_analysis --cov-report=html
```

## 文档

详细文档请参考 `docs/` 目录：

- [配置说明](docs/config.md)
- [数据模型](docs/models.md)
- [股票数据模块](docs/stock_data.md)
- [新闻搜索模块](docs/news_search.md)
- [分析引擎](docs/analysis.md)
- [API接口](docs/api.md)
- [使用示例](docs/examples.md)

## 注意事项

1. **API限制**: 注意各数据源的API调用频率限制
2. **网络连接**: 需要稳定的网络连接获取实时数据
3. **配置要求**: 正确配置API密钥以获得完整功能
4. **投资风险**: 分析结果仅供参考，不构成投资建议
5. **数据时效**: 分析基于当前可获得的数据，具有时效性

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 联系方式

- 项目地址: https://git.atjog.com/aier/financial-analysis
- 问题反馈: 请在项目中提交 Issue