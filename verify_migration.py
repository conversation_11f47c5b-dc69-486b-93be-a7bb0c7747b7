#!/usr/bin/env python3
"""
验证从 GEMINI_API_KEY 到 Windmill 生成文本接口的迁移

这个脚本用于验证项目中所有使用大模型的功能都已经迁移到使用 Windmill 的生成文本接口。
"""

import sys
import os
import re
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from loguru import logger


def check_gemini_api_key_usage() -> Dict[str, List[str]]:
    """检查项目中是否还有使用 GEMINI_API_KEY 的地方"""
    
    issues = {
        "gemini_api_key_references": [],
        "google_generativeai_imports": [],
        "direct_gemini_calls": [],
        "missing_windmill_config": []
    }
    
    # 要检查的文件扩展名
    extensions = ['.py', '.env', '.env.example', '.md']
    
    # 要排除的目录和文件
    exclude_dirs = {'.git', '__pycache__', '.pytest_cache', 'venv', '.venv', 'node_modules'}
    exclude_files = {'verify_migration.py', 'AUGMENT.md'}  # 排除验证脚本本身和文档
    
    project_root = Path('.')
    
    for file_path in project_root.rglob('*'):
        # 跳过目录、排除的目录和排除的文件
        if (file_path.is_dir() or
            any(exclude in file_path.parts for exclude in exclude_dirs) or
            file_path.name in exclude_files):
            continue
            
        # 只检查指定扩展名的文件
        if file_path.suffix not in extensions:
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                
                for line_num, line in enumerate(lines, 1):
                    line_lower = line.lower()
                    
                    # 检查 GEMINI_API_KEY 引用（排除文档中的说明）
                    if ('gemini_api_key' in line_lower and
                        'gemini_api_key_here' not in line_lower and
                        '移除了' not in line and
                        'google gemini api 密钥' not in line_lower):
                        issues["gemini_api_key_references"].append(f"{file_path}:{line_num} - {line.strip()}")
                    
                    # 检查 google.generativeai 导入
                    if 'import google.generativeai' in line or 'from google.generativeai' in line:
                        issues["google_generativeai_imports"].append(f"{file_path}:{line_num} - {line.strip()}")
                    
                    # 检查直接的 Gemini API 调用
                    if 'genai.' in line or 'GenerativeModel' in line:
                        issues["direct_gemini_calls"].append(f"{file_path}:{line_num} - {line.strip()}")
                        
        except Exception as e:
            logger.warning(f"无法读取文件 {file_path}: {e}")
    
    return issues


def check_windmill_configuration() -> Dict[str, Any]:
    """检查 Windmill 配置是否完整"""
    
    config_status = {
        "env_file_exists": False,
        "required_configs": {
            "WINDMILL_BASE_URL": False,
            "WINDMILL_TOKEN": False,
            "WINDMILL_WORKSPACE": False,
            "WINDMILL_FOLDER": False,
            "WINDMILL_SCRIPT": False
        },
        "optional_configs": {
            "GEMINI_MODEL": False
        },
        "deprecated_configs": {
            "GEMINI_API_KEY": False
        }
    }
    
    # 检查 .env 文件
    env_file = Path('.env')
    if env_file.exists():
        config_status["env_file_exists"] = True
        
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检查必需的配置
                for config_key in config_status["required_configs"]:
                    if f"{config_key}=" in content and not f"{config_key}=your_" in content:
                        config_status["required_configs"][config_key] = True
                
                # 检查可选的配置
                for config_key in config_status["optional_configs"]:
                    if f"{config_key}=" in content:
                        config_status["optional_configs"][config_key] = True
                
                # 检查已弃用的配置
                for config_key in config_status["deprecated_configs"]:
                    if f"{config_key}=" in content and not f"{config_key}=your_" in content:
                        config_status["deprecated_configs"][config_key] = True
                        
        except Exception as e:
            logger.error(f"无法读取 .env 文件: {e}")
    
    return config_status


def check_code_migration() -> Dict[str, List[str]]:
    """检查代码迁移状态"""
    
    migration_status = {
        "windmill_calls": [],
        "old_method_names": [],
        "correct_url_format": [],
        "incorrect_url_format": []
    }
    
    python_files = list(Path('.').rglob('*.py'))
    
    for file_path in python_files:
        # 跳过测试文件和虚拟环境
        if any(exclude in str(file_path) for exclude in ['test_', '__pycache__', 'venv', '.venv']):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                
                for line_num, line in enumerate(lines, 1):
                    # 检查 Windmill 调用
                    if 'windmill_base_url' in line and 'api/w/' in line:
                        migration_status["windmill_calls"].append(f"{file_path}:{line_num}")
                    
                    # 检查旧的方法名（排除验证脚本本身）
                    if (('_analyze_via_gemini' in line or '_call_gemini' in line) and
                        'verify_migration.py' not in str(file_path)):
                        migration_status["old_method_names"].append(f"{file_path}:{line_num} - {line.strip()}")
                    
                    # 检查 URL 格式
                    if '/jobs/run/p/f/' in line:
                        migration_status["correct_url_format"].append(f"{file_path}:{line_num}")
                    elif ('api/w/' in line and '/jobs/run/p/f/' not in line and
                          'windmill' in line.lower() and 'verify_migration.py' not in str(file_path)):
                        migration_status["incorrect_url_format"].append(f"{file_path}:{line_num} - {line.strip()}")
                        
        except Exception as e:
            logger.warning(f"无法读取文件 {file_path}: {e}")
    
    return migration_status


def main():
    """主函数"""
    logger.info("开始验证从 GEMINI_API_KEY 到 Windmill 生成文本接口的迁移")
    
    # 1. 检查 GEMINI_API_KEY 使用情况
    logger.info("=== 检查 GEMINI_API_KEY 使用情况 ===")
    api_key_issues = check_gemini_api_key_usage()
    
    has_issues = False
    
    if api_key_issues["gemini_api_key_references"]:
        logger.error("发现 GEMINI_API_KEY 引用:")
        for ref in api_key_issues["gemini_api_key_references"]:
            logger.error(f"  - {ref}")
        has_issues = True
    else:
        logger.success("✅ 未发现 GEMINI_API_KEY 引用")
    
    if api_key_issues["google_generativeai_imports"]:
        logger.error("发现 google.generativeai 导入:")
        for imp in api_key_issues["google_generativeai_imports"]:
            logger.error(f"  - {imp}")
        has_issues = True
    else:
        logger.success("✅ 未发现 google.generativeai 导入")
    
    if api_key_issues["direct_gemini_calls"]:
        logger.error("发现直接的 Gemini API 调用:")
        for call in api_key_issues["direct_gemini_calls"]:
            logger.error(f"  - {call}")
        has_issues = True
    else:
        logger.success("✅ 未发现直接的 Gemini API 调用")
    
    # 2. 检查 Windmill 配置
    logger.info("\n=== 检查 Windmill 配置 ===")
    config_status = check_windmill_configuration()
    
    if not config_status["env_file_exists"]:
        logger.error("❌ .env 文件不存在")
        has_issues = True
    else:
        logger.success("✅ .env 文件存在")
    
    # 检查必需配置
    missing_configs = []
    for config_key, is_present in config_status["required_configs"].items():
        if is_present:
            logger.success(f"✅ {config_key} 已配置")
        else:
            logger.error(f"❌ {config_key} 未配置或为默认值")
            missing_configs.append(config_key)
            has_issues = True
    
    # 检查已弃用配置
    for config_key, is_present in config_status["deprecated_configs"].items():
        if is_present:
            logger.warning(f"⚠️  {config_key} 仍然存在（建议移除）")
    
    # 3. 检查代码迁移
    logger.info("\n=== 检查代码迁移状态 ===")
    migration_status = check_code_migration()
    
    if migration_status["windmill_calls"]:
        logger.success(f"✅ 发现 {len(migration_status['windmill_calls'])} 个 Windmill API 调用")
    else:
        logger.warning("⚠️  未发现 Windmill API 调用")
    
    if migration_status["old_method_names"]:
        logger.error("发现旧的方法名:")
        for method in migration_status["old_method_names"]:
            logger.error(f"  - {method}")
        has_issues = True
    else:
        logger.success("✅ 未发现旧的方法名")
    
    if migration_status["correct_url_format"]:
        logger.success(f"✅ 发现 {len(migration_status['correct_url_format'])} 个正确的 URL 格式")
    
    if migration_status["incorrect_url_format"]:
        logger.error("发现不正确的 URL 格式:")
        for url in migration_status["incorrect_url_format"]:
            logger.error(f"  - {url}")
        has_issues = True
    
    # 4. 总结
    logger.info("\n=== 迁移验证总结 ===")
    if has_issues:
        logger.error("❌ 迁移验证失败，发现问题需要修复")
        if missing_configs:
            logger.info("需要配置的环境变量:")
            for config in missing_configs:
                logger.info(f"  - {config}")
        return False
    else:
        logger.success("✅ 迁移验证成功，所有功能已迁移到 Windmill 生成文本接口")
        return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
