# 动态数据源管理 - 快速入门指南

## 🚀 5分钟快速上手

### 1. 基础使用

```python
from financial_analysis.data_source_api import data_source_api
from financial_analysis.hot_news_collector import HotNewsCollector

# 查看现有数据源
sources = data_source_api.list_sources()
print(f"当前有 {sources['total']} 个数据源")

# 使用新系统收集数据
collector = HotNewsCollector(use_new_system=True)
news = collector.collect_unified_news()
print(f"收集到 {len(news)} 条新闻")
```

### 2. 添加RSS数据源

```python
# 添加一个RSS数据源
rss_config = {
    "source_id": "my_rss",
    "name": "我的RSS源",
    "source_type": "rss",
    "adapter_class": "RSSAdapter",
    "config": {
        "url": "https://feeds.finance.yahoo.com/rss/2.0/headline"
    }
}

result = data_source_api.create_source(rss_config)
if result["success"]:
    print("✓ RSS数据源添加成功")
    
    # 测试连接
    test = data_source_api.test_source("my_rss")
    print(f"测试结果: {test['success']}")
```

### 3. 添加API数据源

```python
# 添加一个API数据源
api_config = {
    "source_id": "news_api",
    "name": "新闻API",
    "source_type": "api", 
    "adapter_class": "APIAdapter",
    "config": {
        "url": "https://jsonplaceholder.typicode.com/posts",
        "field_mapping": {
            "title": ["title"],
            "content": ["body"],
            "url": ["id"]  # 用ID作为URL
        }
    }
}

result = data_source_api.create_source(api_config)
if result["success"]:
    print("✓ API数据源添加成功")
```

### 4. 管理数据源

```python
# 列出所有数据源
all_sources = data_source_api.list_sources()
for source_data in all_sources["data"]:
    config = source_data["config"]
    print(f"- {config['name']} ({config['source_id']})")

# 更新数据源配置
data_source_api.update_source("my_rss", {
    "fetch_interval": 600,  # 改为10分钟
    "enabled": True
})

# 禁用数据源
data_source_api.disable_source("my_rss")

# 重新启用
data_source_api.enable_source("my_rss")

# 删除数据源
# data_source_api.delete_source("my_rss")
```

### 5. 收集和分析数据

```python
# 从所有数据源收集数据
result = data_source_api.collect_all_sources(force_refresh=True)

if result["success"]:
    print(f"收集到 {result['count']} 条数据")
    
    # 分析数据
    data = result["data"]
    sources = {}
    for item in data:
        source = item["source_name"]
        sources[source] = sources.get(source, 0) + 1
    
    print("数据源分布:")
    for source, count in sources.items():
        print(f"  {source}: {count} 条")
```

## 🎯 常用数据源配置模板

### RSS源模板
```python
rss_template = {
    "source_id": "your_rss_id",
    "name": "RSS源名称",
    "source_type": "rss",
    "adapter_class": "RSSAdapter",
    "config": {
        "url": "https://example.com/rss.xml"
    },
    "enabled": True,
    "priority": 1,
    "fetch_interval": 300
}
```

### API源模板
```python
api_template = {
    "source_id": "your_api_id",
    "name": "API源名称",
    "source_type": "api",
    "adapter_class": "APIAdapter",
    "config": {
        "url": "https://api.example.com/news",
        "headers": {
            "Authorization": "Bearer YOUR_TOKEN"
        },
        "params": {
            "limit": 50,
            "category": "finance"
        },
        "field_mapping": {
            "title": ["title", "headline"],
            "content": ["content", "description"],
            "url": ["url", "link"],
            "publish_time": ["publishedAt", "created_at"]
        },
        "data_path": ["data", "articles"]
    }
}
```

### Web爬虫模板
```python
web_template = {
    "source_id": "your_web_id",
    "name": "网站爬虫",
    "source_type": "web",
    "adapter_class": "WebAdapter",
    "config": {
        "url": "https://news.example.com",
        "selectors": {
            "container": ".news-item, article",
            "title": "h2, h3, .title",
            "content": ".content, .summary",
            "link": "a",
            "time": ".time, .date"
        }
    }
}
```

## 🔧 预定义数据源

系统提供了一些预定义的数据源，可以直接安装使用：

```python
# 查看预定义数据源
predefined = data_source_api.get_predefined_sources()
print("预定义数据源:")
for source in predefined["data"]:
    print(f"- {source['name']} ({source['source_id']})")

# 安装预定义数据源
install_result = data_source_api.install_predefined_source("tencent_finance")
if install_result["success"]:
    print("✓ 腾讯财经数据源安装成功")
```

## 📊 监控和统计

```python
# 获取统计信息
stats = data_source_api.get_statistics()
data = stats["data"]
print(f"总数据源: {data['total_sources']}")
print(f"启用数据源: {data['enabled_sources']}")
print(f"类型分布: {data['type_distribution']}")

# 获取数据源状态
status = data_source_api.get_source_status("my_rss")
if status:
    print(f"状态: {status['status']}")
    print(f"成功次数: {status['success_count']}")
    print(f"错误次数: {status['error_count']}")
```

## 🛠️ 自定义适配器

如果需要支持特殊的数据源，可以创建自定义适配器：

```python
from financial_analysis.data_source_adapters import DataSourceAdapter, register_adapter
from financial_analysis.models import UnifiedNewsData

class MyCustomAdapter(DataSourceAdapter):
    def validate_config(self):
        return bool(self.config.config.get('custom_url'))
    
    def fetch_data(self):
        # 实现自定义数据获取逻辑
        url = self.config.config['custom_url']
        # ... 获取和处理数据
        return [
            UnifiedNewsData(
                id=self.generate_news_id("标题", "url"),
                title="自定义新闻标题",
                source_id=self.config.source_id,
                source_name=self.config.name,
                fetch_time=datetime.now()
            )
        ]

# 注册自定义适配器
register_adapter('my_custom', MyCustomAdapter)

# 使用自定义适配器
custom_config = {
    "source_id": "custom_source",
    "name": "自定义数据源",
    "source_type": "my_custom",
    "adapter_class": "MyCustomAdapter",
    "config": {
        "custom_url": "https://example.com/api"
    }
}

data_source_api.create_source(custom_config)
```

## 🚨 常见问题

### Q: 如何处理需要认证的API？
```python
api_with_auth = {
    "config": {
        "url": "https://api.example.com/news",
        "headers": {
            "Authorization": "Bearer YOUR_TOKEN",
            "X-API-Key": "YOUR_API_KEY"
        }
    }
}
```

### Q: 如何设置不同的获取频率？
```python
# 高频数据源 - 每5分钟
high_freq = {"fetch_interval": 300}

# 低频数据源 - 每小时
low_freq = {"fetch_interval": 3600}
```

### Q: 如何处理数据源错误？
```python
# 检查数据源状态
status = data_source_api.get_source_status("problematic_source")
if status and status["error_count"] > 0:
    print(f"错误信息: {status['last_error']}")
    
    # 重置错误状态
    adapter = data_source_registry.get_adapter("problematic_source")
    if adapter:
        adapter.reset_status()
```

## 📚 更多资源

- 📖 [完整文档](docs/dynamic_data_source.md)
- 🧪 [测试用例](tests/test_dynamic_data_source.py)
- 💡 [使用示例](examples/dynamic_data_source_example.py)
- 🏗️ [架构说明](README_DYNAMIC_DATA_SOURCE.md)

---

现在你已经掌握了动态数据源管理的基本用法！开始构建你的数据收集系统吧！ 🎉
