#!/usr/bin/env python3
"""
热点信息功能演示脚本

演示如何使用热点信息功能进行新闻收集、分析、缓存和推送。
"""

import os
import sys
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from financial_analysis import (
    hot_news_manager, 
    HotNewsCollector,
    HotNewsAnalyzer,
    HotNewsPusher,
    cache_manager,
    settings
)
from financial_analysis.utils import setup_logging


def print_separator(title=""):
    """打印分隔线"""
    print("\n" + "=" * 60)
    if title:
        print(f" {title} ")
        print("=" * 60)
    else:
        print()


def demo_basic_usage():
    """演示基础使用"""
    print_separator("基础使用演示")
    
    print("1. 收集和处理热点信息...")
    result = hot_news_manager.collect_and_process_news(force_refresh=True)
    
    if result.get("success"):
        print(f"✅ 处理成功: {result['message']}")
        print(f"   收集数量: {result.get('collected_count', 0)}")
        print(f"   分析数量: {result.get('analyzed_count', 0)}")
        print(f"   过滤后数量: {result.get('filtered_count', 0)}")
        print(f"   处理时间: {result.get('processing_time', 0)} 秒")
        
        push_result = result.get('push_result', {})
        if push_result.get('success'):
            print(f"   推送结果: {push_result.get('message', '未知')}")
    else:
        print(f"❌ 处理失败: {result.get('message', '未知错误')}")
    
    print("\n2. 获取最新热点信息...")
    latest_news = hot_news_manager.get_latest_news(limit=5)
    
    if latest_news:
        print(f"✅ 获取到 {len(latest_news)} 条最新新闻:")
        for i, news in enumerate(latest_news, 1):
            print(f"\n   [{i}] {news.title}")
            print(f"       来源: {news.source}")
            print(f"       时间: {news.publish_time.strftime('%Y-%m-%d %H:%M')}")
            print(f"       重要程度: {news.importance_level or '未知'}")
            print(f"       情感倾向: {news.sentiment or '未知'}")
            if news.keywords:
                print(f"       关键词: {', '.join(news.keywords[:3])}")
    else:
        print("❌ 没有获取到新闻数据")


def demo_search_functionality():
    """演示搜索功能"""
    print_separator("搜索功能演示")
    
    search_keywords = ["股票", "市场", "经济", "科技", "AI"]
    
    for keyword in search_keywords:
        print(f"\n搜索关键词: '{keyword}'")
        search_results = hot_news_manager.search_news(keyword, limit=3)
        
        if search_results:
            print(f"✅ 找到 {len(search_results)} 条相关新闻:")
            for i, news in enumerate(search_results, 1):
                print(f"   [{i}] {news.title}")
                print(f"       来源: {news.source}")
                print(f"       相关度: 高" if keyword.lower() in news.title.lower() else "       相关度: 中")
        else:
            print(f"❌ 没有找到包含 '{keyword}' 的新闻")


def demo_news_details():
    """演示新闻详情查询"""
    print_separator("新闻详情查询演示")
    
    latest_news = hot_news_manager.get_latest_news(limit=3)
    
    if latest_news:
        news = latest_news[0]
        print(f"查询新闻详情: {news.news_id}")
        
        news_detail = hot_news_manager.get_news_by_id(news.news_id)
        
        if news_detail:
            print("✅ 新闻详情:")
            print(f"   ID: {news_detail.news_id}")
            print(f"   标题: {news_detail.title}")
            print(f"   来源: {news_detail.source}")
            print(f"   发布时间: {news_detail.publish_time}")
            print(f"   获取时间: {news_detail.fetch_time}")
            
            if news_detail.content:
                content_preview = news_detail.content[:200] + "..." if len(news_detail.content) > 200 else news_detail.content
                print(f"   内容预览: {content_preview}")
            
            if news_detail.summary:
                print(f"   摘要: {news_detail.summary}")
            
            if news_detail.keywords:
                print(f"   关键词: {', '.join(news_detail.keywords)}")
            
            print(f"   分类: {news_detail.category or '未分类'}")
            print(f"   热度评分: {news_detail.heat_score or '未评分'}")
            print(f"   是否历史信息: {'是' if news_detail.is_historical else '否'}")
            print(f"   是否已推送: {'是' if news_detail.is_pushed else '否'}")
            
            if news_detail.url:
                print(f"   原文链接: {news_detail.url}")
        else:
            print(f"❌ 未找到新闻详情: {news.news_id}")
    else:
        print("❌ 没有可查询的新闻")


def demo_statistics():
    """演示统计信息"""
    print_separator("统计信息演示")
    
    stats = hot_news_manager.get_news_statistics()
    
    if stats:
        print("✅ 新闻统计信息:")
        print(f"   总新闻数: {stats.get('total_news', 0)}")
        
        # 分类分布
        category_dist = stats.get('category_distribution', {})
        if category_dist:
            print("\n   分类分布:")
            for category, count in category_dist.items():
                print(f"     {category}: {count} 条")
        
        # 重要程度分布
        importance_dist = stats.get('importance_distribution', {})
        if importance_dist:
            print("\n   重要程度分布:")
            for level, count in importance_dist.items():
                print(f"     {level}: {count} 条")
        
        # 情感分布
        sentiment_dist = stats.get('sentiment_distribution', {})
        if sentiment_dist:
            print("\n   情感倾向分布:")
            for sentiment, count in sentiment_dist.items():
                print(f"     {sentiment}: {count} 条")
        
        # 推送统计
        push_stats = stats.get('push_statistics', {})
        if push_stats:
            print("\n   推送统计:")
            print(f"     总推送数: {push_stats.get('total_pushes', 0)}")
            print(f"     今日推送数: {push_stats.get('today_pushes', 0)}")
            print(f"     推送功能: {'启用' if push_stats.get('push_enabled') else '禁用'}")
        
        # 缓存统计
        cache_stats = stats.get('cache_statistics', {})
        if cache_stats:
            print("\n   缓存统计:")
            print(f"     总缓存数: {cache_stats.get('total_count', 0)}")
            print(f"     活跃缓存数: {cache_stats.get('active_count', 0)}")
            print(f"     过期缓存数: {cache_stats.get('expired_count', 0)}")
    else:
        print("❌ 无法获取统计信息")


def demo_cache_management():
    """演示缓存管理"""
    print_separator("缓存管理演示")
    
    print("1. 缓存统计信息:")
    cache_stats = cache_manager.get_cache_stats()
    
    if cache_stats:
        print(f"   总缓存项: {cache_stats.get('total_count', 0)}")
        print(f"   活跃缓存项: {cache_stats.get('active_count', 0)}")
        print(f"   过期缓存项: {cache_stats.get('expired_count', 0)}")
        print(f"   总访问次数: {cache_stats.get('total_access', 0)}")
    
    print("\n2. 测试缓存功能:")
    
    # 设置测试缓存
    test_data = {"test": "data", "timestamp": datetime.now().isoformat()}
    cache_manager.set("demo_test", test_data, 60)
    print("   ✅ 设置测试缓存")
    
    # 获取缓存
    cached_data = cache_manager.get("demo_test")
    if cached_data:
        print(f"   ✅ 获取缓存成功: {cached_data}")
    else:
        print("   ❌ 获取缓存失败")
    
    # 清理过期缓存
    expired_count = cache_manager.clear_expired()
    print(f"   ✅ 清理了 {expired_count} 个过期缓存")


def demo_individual_components():
    """演示各个组件的独立使用"""
    print_separator("组件独立使用演示")
    
    print("1. 热点信息收集器:")
    collector = HotNewsCollector()
    raw_news = collector.collect_hot_news(force_refresh=True)
    print(f"   收集到 {len(raw_news)} 条原始新闻")
    
    if raw_news:
        print("\n2. 热点信息分析器:")
        analyzer = HotNewsAnalyzer()
        
        # 分析前3条新闻
        sample_news = raw_news[:3]
        analyzed_news = analyzer.analyze_news_batch(sample_news)
        print(f"   分析了 {len(analyzed_news)} 条新闻")
        
        for news in analyzed_news:
            print(f"     - {news.title}")
            print(f"       情感: {news.sentiment}, 重要性: {news.importance_level}")
            print(f"       历史信息: {'是' if news.is_historical else '否'}")
        
        print("\n3. 消息推送器:")
        pusher = HotNewsPusher()
        
        # 过滤非历史信息
        non_historical = [news for news in analyzed_news if not news.is_historical]
        
        if non_historical:
            push_result = pusher.push_news_batch(non_historical[:2])  # 推送前2条
            print(f"   推送结果: {push_result.get('message', '未知')}")
            print(f"   推送数量: {push_result.get('pushed_count', 0)}")
        else:
            print("   没有可推送的新闻（都是历史信息）")


def check_configuration():
    """检查配置"""
    print_separator("配置检查")
    
    print("当前配置:")
    print(f"  热点信息功能: {'启用' if settings.hot_news_enabled else '禁用'}")
    print(f"  获取间隔: {settings.hot_news_fetch_interval} 秒")
    print(f"  缓存时间: {settings.hot_news_cache_duration} 秒")
    print(f"  最大新闻数: {settings.hot_news_max_items}")
    print(f"  历史检查天数: {settings.hot_news_history_check_days}")
    
    print(f"\n  推送功能: {'启用' if settings.hot_news_push_enabled else '禁用'}")
    print(f"  推送间隔: {settings.hot_news_push_interval} 秒")
    print(f"  推送批量大小: {settings.hot_news_push_batch_size}")
    print(f"  最低重要程度: {settings.hot_news_min_importance}")
    
    # 检查关键配置
    config_issues = []
    
    if not settings.hot_news_enabled:
        config_issues.append("热点信息功能已禁用")
    
    if not settings.windmill_base_url:
        config_issues.append("Windmill URL未配置（将使用模拟模式）")
    
    if not settings.windmill_token:
        config_issues.append("Windmill Token未配置（将使用模拟模式）")
    
    if config_issues:
        print("\n⚠️  配置提醒:")
        for issue in config_issues:
            print(f"  - {issue}")
    else:
        print("\n✅ 配置检查通过")


def main():
    """主函数"""
    print("🔥 热点信息功能演示")
    print("=" * 60)
    print("本演示将展示热点信息功能的各个方面，包括:")
    print("- 基础使用")
    print("- 搜索功能")
    print("- 新闻详情查询")
    print("- 统计信息")
    print("- 缓存管理")
    print("- 组件独立使用")
    
    # 设置日志
    setup_logging()
    
    try:
        # 检查配置
        check_configuration()
        
        # 演示各个功能
        demo_basic_usage()
        demo_search_functionality()
        demo_news_details()
        demo_statistics()
        demo_cache_management()
        demo_individual_components()
        
        print_separator("演示完成")
        print("✅ 热点信息功能演示完成！")
        print("\n📖 更多信息请参考:")
        print("   - docs/hot_news.md - 详细文档")
        print("   - tests/test_hot_news.py - 单元测试")
        print("   - .env.example - 配置示例")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n\n❌ 演示过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
