#!/usr/bin/env python3
"""
金融证券分析项目演示脚本

展示项目的主要功能和使用方法。
"""

import os
import sys
from datetime import datetime

# 添加项目路径到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from financial_analysis import AnalysisEngine, StockDataProvider, NewsSearcher
from financial_analysis.config import settings
from financial_analysis.utils import setup_logging


def demo_basic_usage():
    """演示基本使用方法"""
    print("=" * 60)
    print("金融证券分析项目演示")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    
    # 创建分析引擎
    print("\n1. 创建分析引擎...")
    engine = AnalysisEngine()
    
    # 分析股票
    print("\n2. 分析股票 000001 (平安银行)...")
    try:
        report = engine.generate_analysis_report("000001", "SZSE")
        
        if report:
            print("✓ 分析成功!")
            print(f"  股票名称: {report.stock_info.name}")
            print(f"  当前价格: {report.current_price:.2f} {report.stock_info.currency}")
            print(f"  价格变化: {report.price_change:+.2f} ({report.price_change_percent:+.2f}%)")
            print(f"  综合评级: {report.overall_rating}")
            print(f"  风险等级: {report.risk_level}")
            print(f"  新闻情感: {report.news_sentiment}")
            print(f"  投资建议: {report.investment_advice}")
        else:
            print("✗ 分析失败 - 可能是网络问题或配置问题")
            
    except Exception as e:
        print(f"✗ 分析出错: {str(e)}")


def demo_module_usage():
    """演示分模块使用"""
    print("\n" + "=" * 60)
    print("分模块使用演示")
    print("=" * 60)
    
    # 股票数据获取
    print("\n1. 股票数据获取演示...")
    data_provider = StockDataProvider()
    
    try:
        # 获取股票信息
        stock_info = data_provider.get_stock_info("000001", "SZSE")
        if stock_info:
            print(f"✓ 股票信息: {stock_info.name} ({stock_info.symbol})")
            print(f"  交易所: {stock_info.exchange}")
            print(f"  行业: {stock_info.sector}")
        
        # 获取价格数据
        prices = data_provider.get_stock_prices("000001", days=5, exchange="SZSE")
        if prices:
            print(f"✓ 获取到 {len(prices)} 天的价格数据")
            latest_price = prices[-1]
            print(f"  最新收盘价: {latest_price.close_price:.2f}")
            print(f"  成交量: {latest_price.volume:,}")
        
        # 获取技术指标
        indicators = data_provider.get_technical_indicators("000001", "SZSE")
        if indicators:
            print("✓ 技术指标:")
            if indicators.ma5:
                print(f"  5日均线: {indicators.ma5:.2f}")
            if indicators.rsi:
                print(f"  RSI: {indicators.rsi:.2f}")
            if indicators.macd:
                print(f"  MACD: {indicators.macd:.4f}")
                
    except Exception as e:
        print(f"✗ 股票数据获取出错: {str(e)}")
    
    # 新闻搜索
    print("\n2. 新闻搜索演示...")
    news_searcher = NewsSearcher()
    
    try:
        if stock_info:
            # 搜索新闻
            news_items = news_searcher.search_stock_news(stock_info, days=7)
            print(f"✓ 找到 {len(news_items)} 条相关新闻")
            
            if news_items:
                # 显示前3条新闻
                for i, news in enumerate(news_items[:3], 1):
                    print(f"  {i}. {news.title} ({news.sentiment})")
                
                # 分析情感
                sentiment_result = news_searcher.analyze_news_sentiment(news_items)
                print(f"✓ 新闻情感分析:")
                print(f"  整体情感: {sentiment_result['overall_sentiment']}")
                print(f"  正面新闻: {sentiment_result['positive_count']} 条")
                print(f"  负面新闻: {sentiment_result['negative_count']} 条")
                print(f"  中性新闻: {sentiment_result['neutral_count']} 条")
                
                # 生成摘要
                summary = news_searcher.generate_news_summary(news_items, stock_info)
                print(f"✓ 新闻摘要: {summary[:100]}...")
                
    except Exception as e:
        print(f"✗ 新闻搜索出错: {str(e)}")


def demo_batch_analysis():
    """演示批量分析"""
    print("\n" + "=" * 60)
    print("批量分析演示")
    print("=" * 60)
    
    # 股票列表
    stocks = [
        ("000001", "SZSE", "平安银行"),
        ("000002", "SZSE", "万科A"),
        ("600036", "SSE", "招商银行")
    ]
    
    engine = AnalysisEngine()
    results = []
    
    print(f"\n分析 {len(stocks)} 只股票...")
    
    for i, (symbol, exchange, name) in enumerate(stocks, 1):
        print(f"[{i}/{len(stocks)}] 分析 {symbol} ({name})...")
        
        try:
            report = engine.generate_analysis_report(symbol, exchange)
            if report:
                result = {
                    "symbol": symbol,
                    "name": report.stock_info.name,
                    "current_price": report.current_price,
                    "price_change_percent": report.price_change_percent,
                    "overall_rating": report.overall_rating,
                    "risk_level": report.risk_level
                }
                results.append(result)
                print(f"  ✓ {report.stock_info.name} - {report.overall_rating}")
            else:
                print(f"  ✗ 分析失败")
                
        except Exception as e:
            print(f"  ✗ 分析出错: {str(e)}")
    
    # 显示汇总结果
    if results:
        print(f"\n批量分析完成，成功分析 {len(results)} 只股票:")
        print("-" * 60)
        print(f"{'代码':<8} {'名称':<12} {'价格':<8} {'涨跌幅':<8} {'评级':<6} {'风险'}")
        print("-" * 60)
        
        for result in results:
            print(f"{result['symbol']:<8} {result['name'][:10]:<12} "
                  f"{result['current_price']:<8.2f} {result['price_change_percent']:+6.2f}% "
                  f"{result['overall_rating']:<6} {result['risk_level']}")


def check_configuration():
    """检查配置"""
    print("\n" + "=" * 60)
    print("配置检查")
    print("=" * 60)
    
    print("\n当前配置:")
    print(f"  默认交易所: {settings.default_exchange}")
    print(f"  分析天数: {settings.analysis_days}")
    print(f"  新闻搜索天数: {settings.news_search_days}")
    print(f"  缓存时间: {settings.data_cache_duration} 秒")
    print(f"  日志级别: {settings.log_level}")
    
    # 检查关键配置
    config_issues = []

    if not settings.windmill_base_url:
        config_issues.append("Windmill URL未配置（将使用模拟数据）")

    if not settings.windmill_token:
        config_issues.append("Windmill Token未配置（将使用模拟数据）")
    
    if config_issues:
        print("\n⚠️  配置问题:")
        for issue in config_issues:
            print(f"  - {issue}")
        print("\n请检查 .env 文件配置")
    else:
        print("\n✓ 配置检查通过")


def main():
    """主函数"""
    print(f"金融证券分析项目演示 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查配置
    check_configuration()
    
    # 基本使用演示
    demo_basic_usage()
    
    # 分模块使用演示
    demo_module_usage()
    
    # 批量分析演示
    demo_batch_analysis()
    
    print("\n" + "=" * 60)
    print("演示完成!")
    print("=" * 60)
    print("\n更多使用方法请参考:")
    print("  - docs/examples.md - 详细使用示例")
    print("  - docs/api.md - API接口说明")
    print("  - financial-analysis --help - 命令行帮助")


if __name__ == "__main__":
    main()
