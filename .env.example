# 大模型配置（使用Windmill生成文本接口）
GEMINI_MODEL=gemini-pro

# Windmill配置（用于调用部署的大语言模型）
WINDMILL_BASE_URL=https://wm.atjog.com
WINDMILL_TOKEN=your_windmill_token
WINDMILL_WORKSPACE=my-workspace
WINDMILL_FOLDER=gemini
WINDMILL_SCRIPT=js_structured_output

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/financial_analysis.log

# 数据源配置
DEFAULT_EXCHANGE=SSE  # 默认交易所：SSE(上交所), SZSE(深交所), NASDAQ, NYSE等
DATA_CACHE_DURATION=300  # 数据缓存时间（秒）

# 分析配置
ANALYSIS_DAYS=30  # 分析的历史天数
NEWS_SEARCH_DAYS=7  # 新闻搜索的天数范围

# 热点信息配置
HOT_NEWS_ENABLED=true  # 是否启用热点信息功能
HOT_NEWS_FETCH_INTERVAL=300  # 热点信息获取间隔（秒）
HOT_NEWS_CACHE_DURATION=1800  # 热点信息缓存时间（秒）
HOT_NEWS_MAX_ITEMS=100  # 单次获取的最大新闻数量
HOT_NEWS_HISTORY_CHECK_DAYS=3  # 历史信息检查天数

# 热点信息推送配置
HOT_NEWS_PUSH_ENABLED=true  # 是否启用热点信息推送
HOT_NEWS_PUSH_INTERVAL=600  # 推送间隔（秒）
HOT_NEWS_PUSH_BATCH_SIZE=10  # 单次推送的最大数量
HOT_NEWS_MIN_IMPORTANCE=medium  # 推送的最低重要程度：high/medium/low

# 热点信息数据源配置（JSON格式）
HOT_NEWS_CHANNELS=[]  # 留空使用默认配置
