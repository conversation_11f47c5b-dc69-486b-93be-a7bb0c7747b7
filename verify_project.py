#!/usr/bin/env python3
"""
项目验证脚本

验证项目的基本功能是否正常工作。
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("1. 测试模块导入...")
    try:
        from financial_analysis import (
            AnalysisEngine, StockDataProvider, NewsSearcher,
            StockInfo, StockPrice, TechnicalIndicators, NewsItem, AnalysisReport
        )
        from financial_analysis.config import settings
        from financial_analysis.utils import (
            normalize_stock_symbol, format_currency, safe_float
        )
        print("   ✓ 所有模块导入成功")
        return True
    except Exception as e:
        print(f"   ✗ 模块导入失败: {e}")
        return False

def test_models():
    """测试数据模型"""
    print("2. 测试数据模型...")
    try:
        from financial_analysis.models import StockInfo, StockPrice, NewsItem
        
        # 测试StockInfo
        stock_info = StockInfo(
            symbol="000001",
            name="平安银行",
            exchange="SZSE"
        )
        assert stock_info.symbol == "000001"
        assert stock_info.currency == "CNY"  # 默认值
        
        # 测试StockPrice
        price = StockPrice(
            symbol="000001",
            date=datetime.now(),
            open_price=12.50,
            high_price=12.80,
            low_price=12.30,
            close_price=12.65,
            volume=1000000
        )
        assert price.open_price == 12.50
        
        # 测试NewsItem
        news = NewsItem(
            title="测试新闻",
            source="测试来源",
            publish_time=datetime.now(),
            sentiment="positive"
        )
        assert news.sentiment == "positive"
        
        print("   ✓ 数据模型测试通过")
        return True
    except Exception as e:
        print(f"   ✗ 数据模型测试失败: {e}")
        return False

def test_utils():
    """测试工具函数"""
    print("3. 测试工具函数...")
    try:
        from financial_analysis.utils import (
            normalize_stock_symbol, format_currency, safe_float, safe_int
        )
        
        # 测试股票代码标准化
        assert normalize_stock_symbol("SH.600036") == "600036"
        assert normalize_stock_symbol("000001") == "000001"
        
        # 测试货币格式化
        assert format_currency(15000, "CNY") == "¥1.50万"
        assert format_currency(1500000000, "CNY") == "¥15.00亿"
        
        # 测试安全转换
        assert safe_float("12.5") == 12.5
        assert safe_float("invalid", 0.0) == 0.0
        assert safe_int("123") == 123
        assert safe_int("invalid", 0) == 0
        
        print("   ✓ 工具函数测试通过")
        return True
    except Exception as e:
        print(f"   ✗ 工具函数测试失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("4. 测试配置...")
    try:
        from financial_analysis.config import settings
        
        # 检查配置是否加载
        assert hasattr(settings, 'windmill_base_url')
        assert hasattr(settings, 'windmill_token')
        assert hasattr(settings, 'analysis_days')
        assert hasattr(settings, 'log_level')
        
        # 检查默认值
        assert settings.analysis_days == 30
        assert settings.news_search_days == 7
        assert settings.default_exchange == "SSE"
        
        print("   ✓ 配置测试通过")
        return True
    except Exception as e:
        print(f"   ✗ 配置测试失败: {e}")
        return False

def test_providers():
    """测试数据提供者"""
    print("5. 测试数据提供者...")
    try:
        from financial_analysis import StockDataProvider, NewsSearcher
        
        # 测试StockDataProvider初始化
        provider = StockDataProvider()
        assert hasattr(provider, '_cache')
        assert hasattr(provider, '_cache_timeout')
        
        # 测试NewsSearcher初始化
        searcher = NewsSearcher()
        assert hasattr(searcher, '_cache')
        assert hasattr(searcher, '_cache_timeout')
        
        print("   ✓ 数据提供者测试通过")
        return True
    except Exception as e:
        print(f"   ✗ 数据提供者测试失败: {e}")
        return False

def test_analysis_engine():
    """测试分析引擎"""
    print("6. 测试分析引擎...")
    try:
        from financial_analysis import AnalysisEngine
        
        # 测试AnalysisEngine初始化
        engine = AnalysisEngine()
        assert hasattr(engine, 'stock_data_provider')
        assert hasattr(engine, 'news_searcher')
        
        print("   ✓ 分析引擎测试通过")
        return True
    except Exception as e:
        print(f"   ✗ 分析引擎测试失败: {e}")
        return False

def test_cli():
    """测试命令行接口"""
    print("7. 测试命令行接口...")
    try:
        from financial_analysis.main import FinancialAnalysisCLI
        
        # 测试CLI初始化
        cli = FinancialAnalysisCLI()
        assert hasattr(cli, 'engine')
        
        # 测试参数解析器创建
        parser = cli._create_parser()
        assert parser is not None
        
        print("   ✓ 命令行接口测试通过")
        return True
    except Exception as e:
        print(f"   ✗ 命令行接口测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("金融证券分析项目验证")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_models,
        test_utils,
        test_config,
        test_providers,
        test_analysis_engine,
        test_cli
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"验证结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 项目验证成功！所有功能正常工作。")
        print("\n下一步:")
        print("1. 配置真实的API密钥（.env文件）")
        print("2. 运行 python demo.py 进行完整演示")
        print("3. 使用 financial-analysis --help 查看命令行帮助")
        return True
    else:
        print("❌ 项目验证失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
